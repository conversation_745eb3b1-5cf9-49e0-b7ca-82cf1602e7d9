# 薪酬变动接口文档

## 1. 接口概述

薪酬变动模块用于管理员工薪酬的变更记录，包括变更类目、变更前后信息、变更时间等。本文档详细描述了薪酬变动相关的接口定义、请求参数和响应结果。

## 2. 基础信息

- **接口前缀**: `/oa/salary-changes`
- **数据表**: `t_salary_changes`
- **权限前缀**: `oa:salary-changes`

## 3. 接口列表

### 3.1 创建薪酬变动

- **接口路径**: `POST /oa/salary-changes/create`
- **接口描述**: 创建新的薪酬变动记录
- **权限标识**: `oa:salary-changes:create`

#### 请求参数

| 参数名 | 类型 | 是否必须 | 描述 | 示例值 |
| ------ | ---- | -------- | ---- | ------ |
| personId | Long | 是 | 变更对象员工ID | 7493 |
| changeCategory | String | 是 | 变更金额类目 | 基本工资 |
| infoBeforeChange | String | 是 | 变更前信息 | 5000元 |
| infoAfterChange | String | 是 | 变更后信息 | 5500元 |
| changeTime | String | 否 | 变更日期(自动填充当前时间) | 2023-05-01 |
| changeUserId | Long | 否 | 操作人ID(自动填充当前登录用户) | 27844 |

> 注意：changeTime和changeUserId会在服务端自动填充，前端无需传入

#### 响应结果

```json
{
  "code": 0,
  "data": 1001,  // 新创建的薪酬变动记录ID
  "msg": ""
}
```

### 3.2 更新薪酬变动

- **接口路径**: `PUT /oa/salary-changes/update`
- **接口描述**: 更新已有的薪酬变动记录
- **权限标识**: `oa:salary-changes:update`

#### 请求参数

| 参数名 | 类型 | 是否必须 | 描述 | 示例值 |
| ------ | ---- | -------- | ---- | ------ |
| id | Long | 是 | 薪酬变动记录ID | 29645 |
| personId | Long | 是 | 变更对象员工ID | 7493 |
| changeCategory | String | 是 | 变更金额类目 | 基本工资 |
| infoBeforeChange | String | 是 | 变更前信息 | 5000元 |
| infoAfterChange | String | 是 | 变更后信息 | 5500元 |
| changeTime | String | 否 | 变更日期(自动更新为当前时间) | 2023-05-01 |
| changeUserId | Long | 否 | 操作人ID(自动更新为当前登录用户) | 27844 |

> 注意：changeTime和changeUserId会在服务端自动更新，前端无需传入

#### 响应结果

```json
{
  "code": 0,
  "data": true,
  "msg": ""
}
```

### 3.3 删除薪酬变动

- **接口路径**: `DELETE /oa/salary-changes/delete`
- **接口描述**: 删除薪酬变动记录
- **权限标识**: `oa:salary-changes:delete`

#### 请求参数

| 参数名 | 类型 | 是否必须 | 描述 | 示例值 |
| ------ | ---- | -------- | ---- | ------ |
| id | Long | 是 | 薪酬变动记录ID | 1024 |

#### 响应结果

```json
{
  "code": 0,
  "data": true,
  "msg": ""
}
```

### 3.4 获取薪酬变动详情

- **接口路径**: `GET /oa/salary-changes/get`
- **接口描述**: 获取单个薪酬变动记录的详细信息
- **权限标识**: `oa:salary-changes:query`

#### 请求参数

| 参数名 | 类型 | 是否必须 | 描述 | 示例值 |
| ------ | ---- | -------- | ---- | ------ |
| id | Long | 是 | 薪酬变动记录ID | 1024 |

#### 响应结果

```json
{
  "code": 0,
  "data": {
    "id": 29645,
    "personId": 7493,
    "changeCategory": "基本工资",
    "infoBeforeChange": "5000元",
    "infoAfterChange": "5500元",
    "changeTime": "2023-05-01 10:00:00",
    "changeUserId": 27844,
    "createTime": "2023-05-01 10:00:00",
    "updateTime": "2023-05-01 10:00:00",
    "name": "王五",
    "companyName": "无限想象力公司",
    "postName": "职员",
    "deptName": "业务部",
    "idCardNumber": "123456789012345678",
    "changeUserName": "张三"
  },
  "msg": ""
}
```

### 3.5 获取薪酬变动分页列表

- **接口路径**: `GET /oa/salary-changes/page`
- **接口描述**: 分页查询薪酬变动记录
- **权限标识**: `oa:salary-changes:query`

#### 请求参数

| 参数名 | 类型 | 是否必须 | 描述 | 示例值 |
| ------ | ---- | -------- | ---- | ------ |
| pageNo | Integer | 是 | 页码，从1开始 | 1 |
| pageSize | Integer | 是 | 每页记录数 | 10 |
| name | String | 否 | 员工姓名，模糊匹配 | 王五 |
| companyName | String | 否 | 所属公司，模糊匹配 | 无限想象力公司 |
| deptName | String | 否 | 部门名称，模糊匹配 | 业务部 |
| postName | String | 否 | 职务名称，模糊匹配 | 职员 |
| changeTimeStart | String | 否 | 变更日期开始时间 | 2023-01-01 00:00:00 |
| changeTimeEnd | String | 否 | 变更日期结束时间 | 2023-12-31 23:59:59 |

#### 响应结果

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 29645,
        "personId": 7493,
        "changeCategory": "基本工资",
        "infoBeforeChange": "5000元",
        "infoAfterChange": "5500元",
        "changeTime": "2023-05-01 10:00:00",
        "changeUserId": 27844,
        "createTime": "2023-05-01 10:00:00",
        "updateTime": "2023-05-01 10:00:00",
        "name": "王五",
        "companyName": "无限想象力公司",
        "postName": "职员",
        "deptName": "业务部",
        "idCardNumber": "123456789012345678",
        "changeUserName": "张三"
      }
      // 更多记录...
    ],
    "total": 100
  },
  "msg": ""
}
```

### 3.6 导出薪酬变动Excel

- **接口路径**: `GET /oa/salary-changes/export-excel`
- **接口描述**: 导出薪酬变动记录为Excel文件
- **权限标识**: `oa:salary-changes:export`

#### 请求参数

与获取薪酬变动分页列表接口相同，但不需要传入pageNo和pageSize参数。

#### 响应结果

直接下载Excel文件，文件名为"薪酬变动.xls"。

## 4. 数据结构

### 4.1 薪酬变动实体(SalaryChangesDO)

| 字段名 | 类型 | 描述 | 示例值 |
| ------ | ---- | ---- | ------ |
| id | Long | 主键ID | 29645 |
| personId | Long | 变更对象员工ID | 7493 |
| changeCategory | String | 变更金额类目 | 基本工资 |
| infoBeforeChange | String | 变更前信息 | 5000元 |
| infoAfterChange | String | 变更后信息 | 5500元 |
| changeTime | String | 变更日期 | 2023-05-01 10:00:00 |
| changeUserId | Long | 操作人ID | 27844 |
| createTime | LocalDateTime | 创建时间 | 2023-05-01 10:00:00 |
| updateTime | LocalDateTime | 更新时间 | 2023-05-01 10:00:00 |
| creator | String | 创建人 | admin |
| updater | String | 更新人 | admin |
| deleted | Boolean | 是否删除 | false |

### 4.2 薪酬变动分页请求VO(SalaryChangesPageReqVO)

| 字段名 | 类型 | 描述 | 示例值 |
| ------ | ---- | ---- | ------ |
| pageNo | Integer | 页码 | 1 |
| pageSize | Integer | 每页条数 | 10 |
| name | String | 员工姓名 | 王五 |
| companyName | String | 所属公司 | 无限想象力公司 |
| deptName | String | 部门名称 | 业务部 |
| postName | String | 职务名称 | 职员 |
| idCardNumber | String | 个人证件号 | 123456789012345678 |
| changeTimeStart | String | 变更日期开始时间 | 2023-01-01 00:00:00 |
| changeTimeEnd | String | 变更日期结束时间 | 2023-12-31 23:59:59 |

### 4.3 薪酬变动保存请求VO(SalaryChangesSaveReqVO)

| 字段名 | 类型 | 描述 | 示例值 |
| ------ | ---- | ---- | ------ |
| id | Long | 主键ID，更新时必填 | 29645 |
| personId | Long | 变更对象员工ID | 7493 |
| changeCategory | String | 变更金额类目 | 基本工资 |
| infoBeforeChange | String | 变更前信息 | 5000元 |
| infoAfterChange | String | 变更后信息 | 5500元 |
| changeTime | String | 变更日期(自动填充) | 2023-05-01 |
| changeUserId | Long | 操作人ID(自动填充) | 27844 |

## 5. 注意事项

1. 创建和更新薪酬变动记录时，`changeTime`和`changeUserId`字段会在服务端自动填充为当前时间和当前登录用户ID，前端无需传入。

2. 查询薪酬变动列表时，如果只传入`changeTimeStart`而不传入`changeTimeEnd`，系统会自动将`changeTimeEnd`设置为当前时间。

3. 导出Excel功能会导出所有符合查询条件的记录，不受分页限制。

4. 所有接口都需要相应的权限才能访问，请确保用户具有对应的权限。

5. 删除操作为逻辑删除，数据库中的记录不会被物理删除，而是将`deleted`字段设置为`true`。
