<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yudao-module-pay</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>yudao-module-pay-api</module>
        <module>yudao-module-pay-biz</module>
        <module>yudao-spring-boot-starter-biz-pay</module>
    </modules>

    <name>${project.artifactId}</name>
    <description>
        pay 模块，我们放支付业务，提供业务的支付能力。
        例如说：商户、应用、支付、退款等等
    </description>

</project>
