package cn.iocoder.yudao.module.oa.service.busroutes;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.oa.controller.admin.busroutes.vo.*;
import cn.iocoder.yudao.module.oa.controller.admin.busstations.vo.BusStationsSaveRoutesVO;
import cn.iocoder.yudao.module.oa.controller.admin.bustrips.vo.AppBusTripDetail;
import cn.iocoder.yudao.module.oa.controller.admin.pathways.vo.PathWaysSaveRoutesVO;
import cn.iocoder.yudao.module.oa.dal.dataobject.busroutes.BusRoutesDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.busstations.BusStationsDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.bustrips.BusTripsDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.pathways.PathWaysDO;
import cn.iocoder.yudao.module.oa.dal.mysql.busroutes.BusRoutesMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.busstations.BusStationsMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.pathways.PathWaysMapper;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Array;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;


import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.BUS_ROUTES_NOT_EXISTS;


/**
 * 线路列 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BusRoutesServiceImpl implements BusRoutesService {

    @Resource
    private BusRoutesMapper busRoutesMapper;
    @Resource
    private BusStationsMapper busStationsMapper;
    @Resource
    private PathWaysMapper pathWaysMapper;

    private static final AtomicInteger sequence = new AtomicInteger(0);
    @Override
    public Long createBusRoutes(BusRoutesSaveRequestVO createReqVO) {
        BusRoutesDO busRoutes = busRoutesMapper.selectOne(BusRoutesDO::getRouteName, createReqVO.getRouteName());
        if(busRoutes != null){
            throw exception(new ErrorCode(2_100_004, "您创建的路线名称已在系统中存在，请仔细核对信息"));
        }


        // 插入
        BusStationsDO endStation = createReqVO.getEndStation();
        BusStationsDO startStation = createReqVO.getStartStation();

        List<BusStationsDO> endStations = busStationsMapper.selectStation(endStation.getStationLatitude(),endStation.getStationLongitude());
        if(endStations.size()==0){
            busStationsMapper.insert(endStation);
        }

        List<BusStationsDO> startStations = busStationsMapper.selectStation(startStation.getStationLatitude(),startStation.getStationLongitude());
        if(startStations.size()==0){
            busStationsMapper.insert(startStation);
        }


        for (BusStationsSaveRoutesVO wayDownpoint : createReqVO.getWayDownpoints()) {
           List<BusStationsDO> stations = busStationsMapper.selectStation(wayDownpoint.getStationLatitude(),wayDownpoint.getStationLongitude());
           if(stations.size()==0){
               BusStationsDO busStations = BeanUtils.toBean(wayDownpoint, BusStationsDO.class);
               busStationsMapper.insert(busStations);
           }
        }

        for (PathWaysSaveRoutesVO wayPoint : createReqVO.getWayPoints()) {

            List<PathWaysDO> pathWays= pathWaysMapper.selectWayPoint(wayPoint.getWayName());
            if(pathWays.size()==0){
                PathWaysDO pathWaysDO = BeanUtils.toBean(wayPoint, PathWaysDO.class);
                pathWaysMapper.insert(pathWaysDO);
            }
        }

        List<Integer> startStationId =busStationsMapper.selectIdByLaLo(startStation.getStationLatitude(),startStation.getStationLongitude());
        List<Integer> endStationId =busStationsMapper.selectIdByLaLo(endStation.getStationLatitude(),endStation.getStationLongitude());

        ArrayList<Map<String, Integer>> wayPointsList = new ArrayList<>();
        List<PathWaysSaveRoutesVO> wayPoints = createReqVO.getWayPoints();

        for (PathWaysSaveRoutesVO wayPoint : wayPoints) {
            Integer sortId = wayPoint.getSortId();
            List<Integer> stationIds = pathWaysMapper.selectIdByLaLo(
                    wayPoint.getWayName()
            );
            Integer stationId = stationIds.get(stationIds.size()-1);
            Map<String, Integer> pointMap = new HashMap<>();
            pointMap.put("sortId", sortId);
            pointMap.put("stationId", stationId);
            wayPointsList.add(pointMap);
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("wayPoints", wayPointsList);

        ObjectMapper objectMapper = new ObjectMapper();
        String wayPointsString;
        try {
            wayPointsString = objectMapper.writeValueAsString(resultMap);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate JSON", e);
        }





        //处理下车站点
        ArrayList<Map<String, Integer>> wayDownPointsList = new ArrayList<>();
        List<BusStationsSaveRoutesVO> wayDownPoints = createReqVO.getWayDownpoints();
        for (BusStationsSaveRoutesVO wayPoint : wayDownPoints) {
            Integer sortId = wayPoint.getSortId();
            List<Integer> stationId = busStationsMapper.selectIdByLaLo(
                    wayPoint.getStationLatitude(),
                    wayPoint.getStationLongitude()
            );

            Map<String, Integer> pointMap = new HashMap<>();
            pointMap.put("sortId", sortId);
            pointMap.put("stationId", stationId.get(stationId.size()-1));
            wayDownPointsList.add(pointMap);
        }

        Map<String, Object> resultDownMap = new HashMap<>();
        resultDownMap.put("wayPoints", wayDownPointsList);

        ObjectMapper objectDownMapper = new ObjectMapper();
        String wayDownPointsString;
        try {
            wayDownPointsString = objectDownMapper.writeValueAsString(resultDownMap);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate JSON", e);
        }


        BusRoutesDO busRoutesDO = BeanUtils.toBean(createReqVO, BusRoutesDO.class);




        // 1. 获取当前日期（格式：yyyyMMdd）
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String dateStr = dateFormat.format(new Date());

        // 2. 生成4位流水号（0001-9999）
        int seq = sequence.incrementAndGet() % 10000; // 防止溢出
        String seqStr = String.format("%04d", seq);   // 补零至4位


        busRoutesDO.setRouteNumber(dateStr + seqStr);
        busRoutesDO.setEndStationId(endStationId.get(endStationId.size()-1));
        busRoutesDO.setStartStationId(startStationId.get(startStationId.size()-1));
        busRoutesDO.setWayPoints(wayPointsString);
        busRoutesDO.setWayDownpoints(wayDownPointsString);
        busRoutesDO.setRouteStatus(2);

        busRoutesMapper.insert(busRoutesDO);
        // 返回
        return busRoutesDO.getId();
    }

    @Override
    public void updateBusRoutes(BusRoutesSaveRequestVO updateReqVO) {
        // 校验存在
        validateBusRoutesExists(updateReqVO.getId());
        // 插入
        BusStationsDO endStation = updateReqVO.getEndStation();
        BusStationsDO startStation = updateReqVO.getStartStation();

        busStationsMapper.updateById(endStation);
        busStationsMapper.updateById(startStation);

        for (BusStationsSaveRoutesVO wayDownpoint : updateReqVO.getWayDownpoints()) {

            BusStationsDO busStations = BeanUtils.toBean(wayDownpoint, BusStationsDO.class);
            if(busStations.getStationLatitude()==null || busStations.getStationLongitude()==null){
                busStationsMapper.updateById(busStations);
            }else{
                busStationsMapper.insert(busStations);
            }


        }

        for (PathWaysSaveRoutesVO wayPoint : updateReqVO.getWayPoints()) {

                PathWaysDO pathWaysDO = BeanUtils.toBean(wayPoint, PathWaysDO.class);
                List<PathWaysDO> pathWaysDO1 = pathWaysMapper.selectByLocation(pathWaysDO.getStartLocation(), pathWaysDO.getEndLocation());
                if(pathWaysDO1.size()==0){
                    pathWaysMapper.insert(pathWaysDO);
                }else{
                    pathWaysDO.setId(pathWaysDO1.get(0).getId());
                    pathWaysMapper.updateById(pathWaysDO);
                }
        }
        List<Integer> startStationId =busStationsMapper.selectIdByLaLo(startStation.getStationLatitude(),startStation.getStationLongitude());
        List<Integer> endStationId =busStationsMapper.selectIdByLaLo(endStation.getStationLatitude(),endStation.getStationLongitude());

        ArrayList<Map<String, Integer>> wayPointsList = new ArrayList<>();
        List<PathWaysSaveRoutesVO> wayPoints = updateReqVO.getWayPoints();

        for (PathWaysSaveRoutesVO wayPoint : wayPoints) {
            Integer sortId = wayPoint.getSortId();
            List<Double> startLocation = wayPoint.getStart_location();
            List<Double> endLocation = wayPoint.getEnd_location();
            List<PathWaysDO> stationIds = pathWaysMapper.selectByLocation(startLocation.toString(), endLocation.toString());
            Integer stationId = Math.toIntExact(stationIds.get(0).getId());

            Map<String, Integer> pointMap = new HashMap<>();
            pointMap.put("sortId", sortId);
            pointMap.put("stationId", stationId);
            wayPointsList.add(pointMap);
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("wayPoints", wayPointsList);

        ObjectMapper objectMapper = new ObjectMapper();
        String wayPointsString;
        try {
            wayPointsString = objectMapper.writeValueAsString(resultMap);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate JSON", e);
        }

        //处理下车站点
        ArrayList<Map<String, Integer>> wayDownPointsList = new ArrayList<>();
        List<BusStationsSaveRoutesVO> wayDownPoints = updateReqVO.getWayDownpoints();
        for (BusStationsSaveRoutesVO wayPoint : wayDownPoints) {
            Integer sortId = wayPoint.getSortId();
            List<Integer> stationId = busStationsMapper.selectIdByLaLo(
                    wayPoint.getStationLatitude(),
                    wayPoint.getStationLongitude()
            );

            Map<String, Integer> pointMap = new HashMap<>();
            pointMap.put("sortId", sortId);
            pointMap.put("stationId", stationId.get(stationId.size()-1));
            wayDownPointsList.add(pointMap);
        }

        Map<String, Object> resultDownMap = new HashMap<>();
        resultDownMap.put("wayPoints", wayDownPointsList);

        ObjectMapper objectDownMapper = new ObjectMapper();
        String wayDownPointsString;
        try {
            wayDownPointsString = objectDownMapper.writeValueAsString(resultDownMap);
        } catch (Exception e) {
            throw new RuntimeException("解析json失败", e);
        }

        BusRoutesDO busRoutesDO = BeanUtils.toBean(updateReqVO, BusRoutesDO.class);

        busRoutesDO.setEndStationId(endStationId.get(endStationId.size()-1));
        busRoutesDO.setStartStationId(startStationId.get(startStationId.size()-1));
        busRoutesDO.setWayPoints(wayPointsString);
        busRoutesDO.setWayDownpoints(wayDownPointsString);

        busRoutesMapper.updateById(busRoutesDO);
    }

    @Override
    public void deleteBusRoutes(Long id) {
        // 校验存在
        validateBusRoutesExists(id);
        // 删除
        busRoutesMapper.deleteById(id);
    }

    private void validateBusRoutesExists(Long id) {
        if (busRoutesMapper.selectById(id) == null) {
            throw exception(BUS_ROUTES_NOT_EXISTS);
        }
    }

    @Override
    public BusRoutesSaveRequestVO getBusRoutes(Long id) {
        BusRoutesDO busRoutesDO = busRoutesMapper.selectById(id);

        BusRoutesSaveRequestVO busRoutesSaveRequest = new BusRoutesSaveRequestVO();
        busRoutesSaveRequest.setId(busRoutesDO.getId());
        busRoutesSaveRequest.setRouteNumber(busRoutesDO.getRouteNumber());
        busRoutesSaveRequest.setRouteName(busRoutesDO.getRouteName());
        busRoutesSaveRequest.setRouteDetail(busRoutesDO.getRouteDetail());
        busRoutesSaveRequest.setSchool(busRoutesDO.getSchool());


        busRoutesSaveRequest.setStartStation(busRoutesMapper.selectStationById(busRoutesDO.getStartStationId()));
        busRoutesSaveRequest.setEndStation(busRoutesMapper.selectStationById(busRoutesDO.getEndStationId()));

        String wayPoints = busRoutesDO.getWayPoints();
        String wayDownpoints = busRoutesDO.getWayDownpoints();

        ObjectMapper mapper = new ObjectMapper();
        try {
            //途径点处理

            // 将JSON字符串转换回Map结构
            Map<String, Object> resultMap = mapper.readValue(wayPoints, new TypeReference<Map<String, Object>>() {});

            // 获取wayPoints列表
            List<Map<String, Integer>> wayPointsListMap = (List<Map<String, Integer>>) resultMap.get("wayPoints");


            ArrayList<PathWaysSaveRoutesVO> pathWaysSaveRoutesList = new ArrayList<>();

            for (Map<String, Integer> pointMap : wayPointsListMap) {
                Integer sortId = pointMap.get("sortId");
                Integer stationId = pointMap.get("stationId");
                    PathWaysDO pathWaysDO = pathWaysMapper.selectById(stationId);
                PathWaysSaveRoutesVO pathWaysRoutesVO = new PathWaysSaveRoutesVO();
                String endLocation = pathWaysDO.getEndLocation();
                String startLocation = pathWaysDO.getStartLocation();
                String path = pathWaysDO.getPath();
                if(endLocation != null && startLocation != null && path != null){
                    List<Double> endLocationList = mapper.readValue(endLocation, new TypeReference<List<Double>>() {});
                    List<Double> startLocationList = mapper.readValue(startLocation, new TypeReference<List<Double>>() {});
                    List<List<Double>> pathList = mapper.readValue(path, new TypeReference<List<List<Double>>>() {});
                    pathWaysRoutesVO.setEnd_location(endLocationList);
                    pathWaysRoutesVO.setStart_location(startLocationList);
                    pathWaysRoutesVO.setPath(pathList);
                }


                pathWaysRoutesVO.setSortId(sortId);
                pathWaysRoutesVO.setWayName(pathWaysDO.getWayName());
                pathWaysRoutesVO.setId(pathWaysDO.getId());
                pathWaysSaveRoutesList.add(pathWaysRoutesVO);
            }

            busRoutesSaveRequest.setWayPoints(pathWaysSaveRoutesList);


            //下车点处理

            // 将JSON字符串转换回Map结构
            Map<String, Object> resultDownMap = mapper.readValue(wayDownpoints, new TypeReference<Map<String, Object>>() {});

            // 获取wayPoints列表
            List<Map<String, Integer>> wayDownPointsListMap = (List<Map<String, Integer>>) resultDownMap.get("wayPoints");

            ArrayList<BusStationsSaveRoutesVO> wayDownPointsList = new ArrayList<>();

            for (Map<String, Integer> pointMap : wayDownPointsListMap) {
                Integer sortId = pointMap.get("sortId");
                Integer stationId = pointMap.get("stationId");
                if(stationId!=null){
                    BusStationsDO busStationsDO = busStationsMapper.selectById(stationId);
                    if(busStationsDO!=null){
                        BusStationsSaveRoutesVO busStationsSaveRoutesVO = BeanUtils.toBean(busStationsDO, BusStationsSaveRoutesVO.class);
                        busStationsSaveRoutesVO.setSortId(sortId);

                        wayDownPointsList.add(busStationsSaveRoutesVO);
                    }
                }
            }
            busRoutesSaveRequest.setWayDownpoints(wayDownPointsList);

        } catch (Exception e) {
            e.printStackTrace();
        }



        return busRoutesSaveRequest;
    }

    @Override
    public PageResult<BusRoutesRespVO> getBusRoutesPage(BusRoutesPageReqVO pageReqVO) {

        PageResult<BusRoutesDO> busRoutesDOPageResult = busRoutesMapper.selectPage(pageReqVO);
        PageResult<BusRoutesRespVO> busRoutesDOResult = BeanUtils.toBean(busRoutesDOPageResult, BusRoutesRespVO.class);
        for (BusRoutesRespVO busRoutesRespVO : busRoutesDOResult.getList()) {
            busRoutesRespVO.setStartStationName(busRoutesMapper.selectNameById(busRoutesRespVO.getStartStationId()));
            busRoutesRespVO.setEndStationName(busRoutesMapper.selectNameById(busRoutesRespVO.getEndStationId()));

//            //处理途径道路(列表展示途径道路暂时关闭)
//            String wayPoints = busRoutesRespVO.getWayPoints();
            ObjectMapper mapper = new ObjectMapper();
//            List<Integer> stationIds = new ArrayList<>();
//
//            try {
//                Map<String, Object> jsonMap = mapper.readValue(wayPoints, Map.class);
//                List<Map<String, Integer>> points = (List<Map<String, Integer>>) jsonMap.get("wayPoints");
//                for (Map<String, Integer> point : points) {
//                    stationIds.add(point.get("stationId"));
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//
//            List<String> wayNames = new ArrayList<>();
//            for (Integer point : stationIds) {
//                String pointName = pathWaysMapper.selectNameById(point);
//                wayNames.add(pointName);
//            }
//            String crossWayNames = String.join(",", wayNames);
//            busRoutesRespVO.setWayNames(crossWayNames);


            //处理下车站点
            String wayDownPoints = busRoutesRespVO.getWayDownpoints();
            List<Integer> downStationIds = new ArrayList<>();

            try {
                Map<String, Object> jsondownMap = mapper.readValue(wayDownPoints, Map.class);
                List<Map<String, Integer>> points = (List<Map<String, Integer>>) jsondownMap.get("wayPoints");
                for (Map<String, Integer> point : points) {
                    downStationIds.add(point.get("stationId"));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            List<String> downPoints = new ArrayList<>();
            for (Integer point : downStationIds) {
                String pointName = busRoutesMapper.selectNameById(point);
                downPoints.add(pointName);
            }
            String downPointNames = String.join(",", downPoints);
            busRoutesRespVO.setWayDownpointsNames(downPointNames);

        }

        //排序序号赋值
        // 按 createTime 降序排序
        List<BusRoutesRespVO> sortedList = busRoutesDOResult.getList().stream()
                .sorted(Comparator.comparing(BusRoutesRespVO::getCreateTime).reversed())
                .collect(Collectors.toList());

        // 填充序号（1, 2, 3...）
        for (int i = 0; i < sortedList.size(); i++) {
            sortedList.get(i).setRouteIndex(i + 1);
        }

        // 更新 pageResult 的列表
        busRoutesDOResult.setList(sortedList);
        return busRoutesDOResult;
    }

    @Override
    public AppBusTripDetail selectRouteById(Long routeId) {
        AppBusTripDetail appBusTripDetail = new AppBusTripDetail();

        BusRoutesDO busRoutesDO = busRoutesMapper.selectById(routeId);
        if(busRoutesDO==null){
            throw exception(new ErrorCode(2_100_004, "该线路不存在"));
        }

        Integer startStationId = busRoutesDO.getStartStationId();
        Integer endStationId = busRoutesDO.getEndStationId();

        BusStationsDO startBusStationsDO = busStationsMapper.selectById(startStationId);
        BusStationsDO endBusStationsDO = busStationsMapper.selectById(endStationId);

        appBusTripDetail.setStartStation(startBusStationsDO);
        appBusTripDetail.setEndStation(endBusStationsDO);



        String wayPoints = busRoutesDO.getWayPoints();
        String wayDownpoints = busRoutesDO.getWayDownpoints();

        ObjectMapper mapper = new ObjectMapper();
        try {
            //途径点处理

            // 将JSON字符串转换回Map结构
            Map<String, Object> resultMap = mapper.readValue(wayPoints, new TypeReference<Map<String, Object>>() {});

            // 获取wayPoints列表
            List<Map<String, Integer>> wayPointsListMap = (List<Map<String, Integer>>) resultMap.get("wayPoints");

            ArrayList<PathWaysDO> pathWaysList = new ArrayList<>();

            for (Map<String, Integer> pointMap : wayPointsListMap) {
                Integer sortId = pointMap.get("sortId");
                Integer stationId = pointMap.get("stationId");

                if(stationId!=null){
                    PathWaysDO pathWaysDO = pathWaysMapper.selectById(stationId);
                    PathWaysSaveRoutesVO pathWaysRoutesVO = new PathWaysSaveRoutesVO();
                    String endLocation = pathWaysDO.getEndLocation();
                    String startLocation = pathWaysDO.getStartLocation();
                    String path = pathWaysDO.getPath();
                    List<Double> endLocationList = mapper.readValue(endLocation, new TypeReference<List<Double>>() {});
                    List<Double> startLocationList = mapper.readValue(startLocation, new TypeReference<List<Double>>() {});
                    List<List<Double>> pathList = mapper.readValue(path, new TypeReference<List<List<Double>>>() {});
                    pathWaysRoutesVO.setEnd_location(endLocationList);
                    pathWaysRoutesVO.setStart_location(startLocationList);
                    pathWaysRoutesVO.setPath(pathList);
                    pathWaysRoutesVO.setSortId(sortId);
                    pathWaysRoutesVO.setWayName(pathWaysDO.getWayName());
                    pathWaysRoutesVO.setId(pathWaysDO.getId());


                    pathWaysList.add(pathWaysDO);
                }
            }

            appBusTripDetail.setPathWays(pathWaysList);


            //下车点处理
            // 将JSON字符串转换回Map结构
            Map<String, Object> resultDownMap = mapper.readValue(wayDownpoints, new TypeReference<Map<String, Object>>() {});

            // 获取wayPoints列表
            List<Map<String, Integer>> wayDownPointsListMap = (List<Map<String, Integer>>) resultDownMap.get("wayPoints");

            ArrayList<BusStationsSaveRoutesVO> wayDownPointsList = new ArrayList<>();

            for (Map<String, Integer> pointMap : wayDownPointsListMap) {
                Integer sortId = pointMap.get("sortId");
                Integer stationId = pointMap.get("stationId");

                    BusStationsDO busStationsDO = busStationsMapper.selectById(stationId);
                    if(busStationsDO!=null){
                        BusStationsSaveRoutesVO busStationsSaveRoutesVO = BeanUtils.toBean(busStationsDO, BusStationsSaveRoutesVO.class);
                        busStationsSaveRoutesVO.setSortId(sortId);

                        wayDownPointsList.add(busStationsSaveRoutesVO);
                    }
            }
            appBusTripDetail.setWayDownPoints(wayDownPointsList);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return appBusTripDetail;
    }

    @Override
    public PageResult<BusRoutesRespVO> getBusRoutesWaysPage(BusRoutesPageReqVO pageReqVO) {
        PageResult<BusRoutesDO> busRoutesDOPageResult = busRoutesMapper.selectPage(pageReqVO);
        PageResult<BusRoutesRespVO> busRoutesDOResult = BeanUtils.toBean(busRoutesDOPageResult, BusRoutesRespVO.class);
        for (BusRoutesRespVO busRoutesRespVO : busRoutesDOResult.getList()) {
            busRoutesRespVO.setStartStationName(busRoutesMapper.selectNameById(busRoutesRespVO.getStartStationId()));
            busRoutesRespVO.setEndStationName(busRoutesMapper.selectNameById(busRoutesRespVO.getEndStationId()));

            //处理途径道路(列表展示途径道路暂时关闭)
//            String wayPoints = busRoutesRespVO.getWayPoints();
            ObjectMapper mapper = new ObjectMapper();
//            List<Integer> stationIds = new ArrayList<>();
//
//            try {
//                Map<String, Object> jsonMap = mapper.readValue(wayPoints, Map.class);
//                List<Map<String, Integer>> points = (List<Map<String, Integer>>) jsonMap.get("wayPoints");
//                for (Map<String, Integer> point : points) {
//                    stationIds.add(point.get("stationId"));
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//
//            List<String> wayNames = new ArrayList<>();
//            for (Integer point : stationIds) {
//                String pointName = pathWaysMapper.selectNameById(point);
//                wayNames.add(pointName);
//            }
//            String crossWayNames = String.join(",", wayNames);
//            busRoutesRespVO.setWayNames(crossWayNames);


            //处理下车站点
            String wayDownPoints = busRoutesRespVO.getWayDownpoints();
            List<Integer> downStationIds = new ArrayList<>();

            try {
                Map<String, Object> jsondownMap = mapper.readValue(wayDownPoints, Map.class);
                List<Map<String, Integer>> points = (List<Map<String, Integer>>) jsondownMap.get("wayPoints");
                for (Map<String, Integer> point : points) {
                    downStationIds.add(point.get("stationId"));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            List<String> downPoints = new ArrayList<>();
            for (Integer point : downStationIds) {
                String pointName = busRoutesMapper.selectNameById(point);
                downPoints.add(pointName);
            }
            String downPointNames = String.join(",", downPoints);
            busRoutesRespVO.setWayDownpointsNames(downPointNames);

        }

        //排序序号赋值
        // 按 createTime 降序排序
        List<BusRoutesRespVO> sortedList = busRoutesDOResult.getList().stream()
                .sorted(Comparator.comparing(BusRoutesRespVO::getCreateTime).reversed())
                .collect(Collectors.toList());

        // 填充序号（1, 2, 3...）
        for (int i = 0; i < sortedList.size(); i++) {
            sortedList.get(i).setRouteIndex(i + 1);
        }

        // 更新 pageResult 的列表
        busRoutesDOResult.setList(sortedList);
        return busRoutesDOResult;
    }


}
