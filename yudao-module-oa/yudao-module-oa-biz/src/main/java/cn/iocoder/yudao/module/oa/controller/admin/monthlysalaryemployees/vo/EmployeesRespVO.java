package cn.iocoder.yudao.module.oa.controller.admin.monthlysalaryemployees.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 月周人员花名册 Response VO")
@Data
@ExcelIgnoreUnannotated
public class EmployeesRespVO {
    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String name;

    @Schema(description = "手机号码")
    private String phoneNumber;

    @Schema(description = "性别", requiredMode = Schema.RequiredMode.REQUIRED)
    @DictFormat(cn.iocoder.yudao.module.system.enums.DictTypeConstants.USER_SEX)
    private Integer sex;

    @Schema(description = "性别")
    private String sexName;

    @Schema(description = "出生年月", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    @DateTimeFormat(FORMAT_YEAR_MONTH_DAY)
    private String dateOfBirth;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "驾驶证审验时限止")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    @DateTimeFormat(FORMAT_YEAR_MONTH_DAY)
    private String driverLicenseRenewalDateEnd;

    @Schema(description = "准驾车型")
    private String drivingLicenses;

    @Schema(description = "校车驾驶资格取得时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    @DateTimeFormat(FORMAT_YEAR_MONTH_DAY)
    private String schoolBusQualificationDate;

    @Schema(description = "身份证号码")
    private String idCardNumber;

    @Schema(description = "职务ID")
    private Long postId;

    @Schema(description = "职务名称")
    private String postName;

    @Schema(description = "驾驶证有效限期止")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    @DateTimeFormat(FORMAT_YEAR_MONTH_DAY)
    private String driverLicenseExpiryDateEnd;
}