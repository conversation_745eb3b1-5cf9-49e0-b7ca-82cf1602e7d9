package cn.iocoder.yudao.module.oa.controller.admin.fuelreports.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 油耗日月报 Response VO")
@Data
@ExcelIgnoreUnannotated
public class FuelReportsRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5804")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "车牌号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("车牌号")
    private String vehildno;

    @Schema(description = "车辆所属公司", example = "王五")
    @ExcelProperty("车辆所属公司")
    private String companyName;

    @Schema(description = "设备上报日期")
    @ExcelProperty("设备上报日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date businessDate;

    @Schema(description = "行驶里程，单位米")
    @ExcelProperty("行驶里程，单位米")
    private Long licheng;

    @Schema(description = "油耗；100 = 1升")
    @ExcelProperty("油耗；100 = 1升")
    private Integer youHao;

    @Schema(description = "加油；100 = 1升")
    @ExcelProperty("加油；100 = 1升")
    private Integer ayou;

    @Schema(description = "漏油；100 = 1升")
    @ExcelProperty("漏油；100 = 1升")
    private Integer ryou;

    @Schema(description = "是否是月报表")
    @ExcelProperty("是否是月报表")
    private Boolean isMonth;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}