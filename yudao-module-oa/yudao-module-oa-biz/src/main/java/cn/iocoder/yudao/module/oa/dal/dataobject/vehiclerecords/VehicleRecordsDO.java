package cn.iocoder.yudao.module.oa.dal.dataobject.vehiclerecords;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 油量汇总 DO
 *
 * <AUTHOR>
 */
@TableName("t_vehicle_records")
@KeySequence("t_vehicle_records_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VehicleRecordsDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 司机信息
     */
    private String driver;
    /**
     * 车牌号
     */
    private String vehildno;
    /**
     * 车辆所属公司
     */
    private String companyName;
    /**
     * 开始时间(设备上报数据时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;
    /**
     * 结束时间(设备上报数据时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 开始油量；100 = 1升
     */
    private BigDecimal startYouLiang;
    /**
     * 结束油量/升
     */
    private BigDecimal endYouLiang;
    /**
     * 总里程，单位米
     */
    private BigDecimal liCheng;
    /**
     * 总油耗/升
     */
    private BigDecimal youLiang;
    /**
     * 加油量/升
     */
    private BigDecimal addYouLiang;
    /**
     * 时长/小时
     */
    private BigDecimal workTime;

}