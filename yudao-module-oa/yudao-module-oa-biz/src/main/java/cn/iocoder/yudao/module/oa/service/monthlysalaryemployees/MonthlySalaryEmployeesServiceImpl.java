package cn.iocoder.yudao.module.oa.service.monthlysalaryemployees;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.mybatis.core.query.QueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.module.oa.commons.constants.OaConstant;
import cn.iocoder.yudao.module.oa.controller.admin.monthlysalarypersonattachment.vo.MonthlySalaryPersonAttachmentRespVO;
import cn.iocoder.yudao.module.oa.dal.dataobject.monthlysalarypersonattachment.MonthlySalaryPersonAttachmentDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.personinfo.PersonInfoDO;
import cn.iocoder.yudao.module.oa.dal.mysql.monthlysalarypersonattachment.MonthlySalaryPersonAttachmentMapper;
import cn.iocoder.yudao.module.oa.commons.enums.PostTypeEnum;
import cn.iocoder.yudao.module.oa.dal.mysql.personinfo.PersonInfoMapper;
import cn.iocoder.yudao.module.oa.service.monthlysalarypersonattachment.MonthlySalaryPersonAttachmentService;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.PostDO;
import cn.iocoder.yudao.module.system.dal.mysql.dept.DeptMapper;
import cn.iocoder.yudao.module.system.dal.mysql.dept.PostMapper;
import cn.iocoder.yudao.module.system.service.dept.DeptService;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.module.oa.controller.admin.monthlysalaryemployees.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.monthlysalaryemployees.MonthlySalaryEmployeesDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.oa.dal.mysql.monthlysalaryemployees.MonthlySalaryEmployeesMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.*;

/**
 * 月工资人员花名册 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MonthlySalaryEmployeesServiceImpl implements MonthlySalaryEmployeesService {

    @Resource
    private MonthlySalaryEmployeesMapper monthlySalaryEmployeesMapper;
    @Autowired
    private DeptService deptService;
    @Autowired
    private DeptMapper deptMapper;
    @Autowired
    private PostMapper postMapper;
    @Resource
    private MonthlySalaryPersonAttachmentService monthlySalaryEmployeeAttachmentService;
    @Resource
    private MonthlySalaryPersonAttachmentMapper monthlySalaryPersonAttachmentMapper;
    @Resource
    private PersonInfoMapper personInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createMonthlySalaryEmployees(MonthlySalaryEmployeesSaveReqVO createReqVO) {
        //新增或更新的校验
        this.validateUnique(BeanUtils.toBean(createReqVO, MonthlySalaryEmployeesDO.class));

        //处理员工信息
        processEmployeeInfo(createReqVO);
        //校验司机必填项
        validateDriver(createReqVO);
        //转换驾照类型
        setDrivingLicenses(createReqVO);
        // 插入
        MonthlySalaryEmployeesDO monthlySalaryEmployees = BeanUtils.toBean(createReqVO, MonthlySalaryEmployeesDO.class);
        monthlySalaryEmployeesMapper.insert(monthlySalaryEmployees);
        //新增的员工默认状态为在职
        if(monthlySalaryEmployees.getStatus()==null){
            monthlySalaryEmployees.setStatus(OaConstant.EMPLOYEE_STATUS_DUTY);
        }
        //插入附件表信息
        if(!CollectionUtil.isEmpty(createReqVO.getPersonAttachmentList())){
            createReqVO.getPersonAttachmentList().forEach(attachment -> {
                attachment.setPersonId(monthlySalaryEmployees.getId());
                monthlySalaryEmployeeAttachmentService.createMonthlySalaryPersonAttachment(attachment);
            });
        }

        // 返回
        return monthlySalaryEmployees.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMonthlySalaryEmployees(MonthlySalaryEmployeesSaveReqVO updateReqVO) {
        // 校验存在
        validateMonthlySalaryEmployeesExists(updateReqVO.getId());
        //新增或更新的校验
        this.validateUnique(BeanUtils.toBean(updateReqVO, MonthlySalaryEmployeesDO.class));

        //处理员工信息
        processEmployeeInfo(updateReqVO);

        //校验司机必填项
        validateDriver(updateReqVO);
        //转换驾照类型
        setDrivingLicenses(updateReqVO);

        // 更新
        MonthlySalaryEmployeesDO updateObj = BeanUtils.toBean(updateReqVO, MonthlySalaryEmployeesDO.class);
        monthlySalaryEmployeesMapper.updateById(updateObj);

        //更新附件表

        //先删除，后插入
           MonthlySalaryPersonAttachmentRespVO monthlySalaryEmployeeAttachmentRespVO = new MonthlySalaryPersonAttachmentRespVO();
            monthlySalaryEmployeeAttachmentRespVO.setPersonId(updateReqVO.getId());
            List<MonthlySalaryPersonAttachmentDO> getMonthlySalaryPersonAttachmentList = monthlySalaryEmployeeAttachmentService.getMonthlySalaryPersonAttachmentList(monthlySalaryEmployeeAttachmentRespVO);

            //批量删除，先将getMonthlySalaryPersonAttachmentList中的 id 放入list中，然后批量删除
            List<Long> list = getMonthlySalaryPersonAttachmentList.stream()
                .map(MonthlySalaryPersonAttachmentDO::getId)
                .collect(Collectors.toList());

            // 只有当list不为空时才执行批量删除
            if (!list.isEmpty()) {
                monthlySalaryPersonAttachmentMapper.deleteBatchIds(list);
            }
            //批量插入
            if(!CollectionUtil.isEmpty(updateReqVO.getPersonAttachmentList())){
                   updateReqVO.getPersonAttachmentList().forEach(attachment -> {
                attachment.setPersonId(updateReqVO.getId());
                attachment.setId(null);
                monthlySalaryEmployeeAttachmentService.createMonthlySalaryPersonAttachment(attachment);
            });
        }
    }

    /**
     * 离职
     *
     * @param id
     * @return
     */
    @Override
    public int outOffDuty(Long id) {
        // 校验存在
        validateMonthlySalaryEmployeesExists(id);
        //查询人员是否离职
        MonthlySalaryEmployeesDO monthlySalaryEmployeesDO = monthlySalaryEmployeesMapper.selectById(id);
        if (monthlySalaryEmployeesDO.getStatus() == OaConstant.EMPLOYEE_STATUS_OUT_OFF_DUTY) {
            throw exception(new ErrorCode(2_100_004, "人员已离职,无法操作"));
        }
        monthlySalaryEmployeesDO.setStatus(OaConstant.EMPLOYEE_STATUS_OUT_OFF_DUTY);
        return monthlySalaryEmployeesMapper.updateById(monthlySalaryEmployeesDO);
    }

    /**
     * 批量离职
     *
     * @param ids 员工Id数组
     * @return 成功离职的人数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchOutOffDuty(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return 0;
        }

        int successCount = 0;
        for (Long id : ids) {
            try {
                // 校验存在
                validateMonthlySalaryEmployeesExists(id);
                // 查询人员是否离职
                MonthlySalaryEmployeesDO monthlySalaryEmployeesDO = monthlySalaryEmployeesMapper.selectById(id);
                if (monthlySalaryEmployeesDO.getStatus() == OaConstant.EMPLOYEE_STATUS_OUT_OFF_DUTY) {
                    // 已离职的员工跳过
                    continue;
                }
                monthlySalaryEmployeesDO.setStatus(OaConstant.EMPLOYEE_STATUS_OUT_OFF_DUTY);
                monthlySalaryEmployeesMapper.updateById(monthlySalaryEmployeesDO);
                successCount++;
            } catch (Exception e) {
                // 单个员工处理失败不影响其他员工
                log.error("员工[{}]离职处理失败", id, e);
            }
        }
        return successCount;
    }

    @Override
    public void deleteMonthlySalaryEmployees(Long id) {
        // 校验存在
        validateMonthlySalaryEmployeesExists(id);
        // 删除
        monthlySalaryEmployeesMapper.deleteById(id);
    }

    private void validateMonthlySalaryEmployeesExists(Long id) {
        if (monthlySalaryEmployeesMapper.selectById(id) == null) {
            throw exception(MONTHLY_SALARY_EMPLOYEES_NOT_EXISTS);
        }
    }


    /**
     * 处理员工信息（解析身份证、计算年限、校验司机字段等）
     */
    private void processEmployeeInfo(MonthlySalaryEmployeesSaveReqVO reqVO) {
        // 解析身份证信息
        if (StringUtils.isNotBlank(reqVO.getIdCardNumber())&&reqVO.getIdCardNumber().length()>=15) {
            parseIdCardInfo(reqVO);
        }
        //如果输入的不是身份证，就要校验性别、出生年月日是否必填
            if (reqVO.getSex()==null) {
                throw exception(new ErrorCode(2_100_004, "性别不能为空"));
            }
            if (StringUtils.isBlank(reqVO.getDateOfBirth())) {
                throw exception(new ErrorCode(2_100_004, "出生年月日不能为空"));
            }

        // 计算入职年限
        if (reqVO.getYearsOfService() == null) {
            calculateYearsOfService(reqVO);
        }

        // 计算合同年限
        if (reqVO.getContractYears() == null) {
            calculateContractYears(reqVO);
        }

        // 校验司机必填项
        validateDriver(reqVO);

        // 转换驾照类型
        setDrivingLicenses(reqVO);
    }

    /**
     * 解析身份证信息
     */
    private void parseIdCardInfo(MonthlySalaryEmployeesSaveReqVO reqVO) {
        try {
            // 首先校验身份证有效性
            boolean validCard = IdcardUtil.isValidCard(reqVO.getIdCardNumber());
            if (!validCard) {
                throw exception(new ErrorCode(2_100_004, "身份证:" + reqVO.getIdCardNumber() + "格式有误"));
            }

            // 解析出生日期
            String birth = IdcardUtil.getBirth(reqVO.getIdCardNumber());
            reqVO.setDateOfBirth(birth);

            // 解析性别
            int genderByIdCard = IdcardUtil.getGenderByIdCard(reqVO.getIdCardNumber());
            if (genderByIdCard == 1) {
                reqVO.setSex(OaConstant.EMPLOYEE_SEX_MALE);
            } else if (genderByIdCard == 0) {
                reqVO.setSex(OaConstant.EMPLOYEE_SEX_FEMALE);
            }
        } catch (Exception e) {
            log.error("身份证:{}解析失败", e.getMessage());
            throw exception(new ErrorCode(2_100_004, "身份证解析失败：" + e.getMessage()));
        }
    }

    /**
     * 计算入职年限
     */
    private void calculateYearsOfService(MonthlySalaryEmployeesSaveReqVO reqVO) {
        if (reqVO.getStartDate() == null) {
            log.error("入职日期不能为空");
            throw exception(new ErrorCode(400, "入职日期不能为空"));
        }

        LocalDateTime startDateTime = LocalDateTimeUtil.of(reqVO.getStartDate());
        long yearsOfService = LocalDateTimeUtil.between(startDateTime, LocalDateTime.now(), ChronoUnit.YEARS);
        reqVO.setYearsOfService((int) yearsOfService);
    }

    /**
     * 计算合同年限
     */
    private void calculateContractYears(MonthlySalaryEmployeesSaveReqVO reqVO) {
        // 如果合同起止日期有任一为空，则不计算合同年限
        if (reqVO.getContractStartDate() == null || reqVO.getContractEndDate() == null) {
            log.debug("合同期限起止日期不完整，不计算合同年限");
            return; // 不再抛出异常，直接返回
        }

        long yearsOfContract = DateUtil.betweenYear(
                DateUtil.parse(reqVO.getContractStartDate(), "yyyy-MM-dd"),
                DateUtil.parse(reqVO.getContractEndDate(), "yyyy-MM-dd"),
                false
        );
        reqVO.setContractYears((int) yearsOfContract);
    }


    /**
     * 校验司机相关字段是否填写完整
     *
     */
    public void validateDriver(MonthlySalaryEmployeesSaveReqVO monthlySalaryEmployeesSaveReqVO) {
        if(monthlySalaryEmployeesSaveReqVO==null){
            return;
        }
        // 获取驾驶证相关字段
        String driverLicenseArchiveNumber = monthlySalaryEmployeesSaveReqVO.getDriverLicenseArchiveNumber();
        String driverLicenseExpiryDateStart = monthlySalaryEmployeesSaveReqVO.getDriverLicenseExpiryDateStart();
        String driverLicenseExpiryDateEnd = monthlySalaryEmployeesSaveReqVO.getDriverLicenseExpiryDateEnd();
        String initialLicenseIssueDate = monthlySalaryEmployeesSaveReqVO.getInitialLicenseIssueDate();
        String driverLicenseRenewalDateStart = monthlySalaryEmployeesSaveReqVO.getDriverLicenseRenewalDateStart();
        String driverLicenseRenewalDateEnd = monthlySalaryEmployeesSaveReqVO.getDriverLicenseRenewalDateEnd();
        String drivingLicenses = monthlySalaryEmployeesSaveReqVO.getDrivingLicenses();

        // 获取校车驾驶资格取得时间
        String schoolBusQualificationDate = monthlySalaryEmployeesSaveReqVO.getSchoolBusQualificationDate();

        // 判断是否有任意一个驾驶证字段被填写
        boolean isAnyDriverFieldFilled =
                StringUtils.isNotBlank(driverLicenseArchiveNumber) ||
                        StringUtils.isNotBlank(driverLicenseExpiryDateStart) ||
                        StringUtils.isNotBlank(driverLicenseExpiryDateEnd) ||
                        StringUtils.isNotBlank(initialLicenseIssueDate)  ||
                        StringUtils.isNotBlank(driverLicenseRenewalDateStart)  ||
                        StringUtils.isNotBlank(driverLicenseRenewalDateEnd)  ||
                        StringUtils.isNotBlank((drivingLicenses));

        // 如果填写了校车驾驶资格取得时间，则驾驶证相关字段必须全部填写
        if (schoolBusQualificationDate != null) {
            if (StringUtils.isBlank(driverLicenseArchiveNumber)) {
                throw new ServiceException(new ErrorCode(2_200_001, "校车驾驶资格取得时间已填写，驾驶证档案编号不能为空"));
            }
            if (driverLicenseExpiryDateStart == null) {
                throw new ServiceException(new ErrorCode(2_200_001, "校车驾驶资格取得时间已填写，驾驶证有效期限起不能为空"));
            }
            if (driverLicenseExpiryDateEnd == null) {
                throw new ServiceException(new ErrorCode(2_200_001, "校车驾驶资格取得时间已填写，驾驶证有效期限止不能为空"));
            }
            if (initialLicenseIssueDate == null) {
                throw new ServiceException(new ErrorCode(2_200_001, "校车驾驶资格取得时间已填写，初次领证日期不能为空"));
            }
            if (driverLicenseRenewalDateStart == null) {
                throw new ServiceException(new ErrorCode(2_200_001, "校车驾驶资格取得时间已填写，驾驶证审验时限起不能为空"));
            }
            if (driverLicenseRenewalDateEnd == null) {
                throw new ServiceException(new ErrorCode(2_200_001, "校车驾驶资格取得时间已填写，驾驶证审验时限止不能为空"));
            }
            if (StringUtils.isBlank(drivingLicenses)) {
                throw new ServiceException(new ErrorCode(2_200_001, "校车驾驶资格取得时间已填写，准驾车型不能为空"));
            }
        }

        // 如果任意一个驾驶证字段被填写，则其余驾驶证字段也必须填写
        if (isAnyDriverFieldFilled) {
            if (StringUtils.isBlank(driverLicenseArchiveNumber)) {
                throw new ServiceException(new ErrorCode(2_200_001, "驾驶证档案编号不能为空"));
            }
            if (driverLicenseExpiryDateStart == null) {
                throw new ServiceException(new ErrorCode(2_200_001, "驾驶证有效期限起不能为空"));
            }
            if (driverLicenseExpiryDateEnd == null) {
                throw new ServiceException(new ErrorCode(2_200_001, "驾驶证有效期限止不能为空"));
            }
            if (initialLicenseIssueDate == null) {
                throw new ServiceException(new ErrorCode(2_200_001, "初次领证日期不能为空"));
            }
            if (driverLicenseRenewalDateStart == null) {
                throw new ServiceException(new ErrorCode(2_200_001, "驾驶证审验时限起不能为空"));
            }
            if (driverLicenseRenewalDateEnd == null) {
                throw new ServiceException(new ErrorCode(2_200_001, "驾驶证审验时限止不能为空"));
            }
            if (StringUtils.isBlank(drivingLicenses)) {
                throw new ServiceException(new ErrorCode(2_200_001, "准驾车型不能为空"));
            }
        }
    }

    /**
     * 设置驾驶证类型
     */
    public void setDrivingLicenses(MonthlySalaryEmployeesSaveReqVO monthlySalaryEmployeesSaveReqVO){
//        if (StringUtils.isNotEmpty(monthlySalaryEmployeesSaveReqVO.getDrivingLicenses())){
//            List<String> licenseTypeList = new ArrayList<String>();
//            String[] arry= Convert.toStrArray(monthlySalaryEmployeesSaveReqVO.getDrivingLicenses());
//            Collections.addAll(licenseTypeList, arry);
//            String licenseTypeJson= JSONArray.toJSONString(licenseTypeList);
//            monthlySalaryEmployeesSaveReqVO.setDrivingLicenses(licenseTypeJson);
//        }
    }




    /**
     * 校验字段唯一性（通用方法）
     *
     * @param fieldName  字段名
     * @param fieldDesc
     * @param fieldValue 字段值
     * @param id         当前记录的 ID（新增时为 null 或 0，修改时为有效 ID）
     */
    private void validateFieldUnique(String fieldName, String fieldDesc,Object fieldValue, Long id) {
        if (fieldValue == null) {
            return; // 如果字段值为空，跳过校验
        }

        if(StringUtils.isBlank(fieldDesc)){
            fieldDesc=fieldName;
        }
        QueryWrapperX<MonthlySalaryEmployeesDO> queryWrapper = new QueryWrapperX<>();
        queryWrapper.eq("deleted", false)
                .eq(fieldName, fieldValue);

        // 修改场景：排除当前记录本身
        if (id != null && id > 0) {
            queryWrapper.ne("id", id);
        }

        Long count = monthlySalaryEmployeesMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw exception(new ErrorCode(2_100_004, "新增或修改月工资花名册失败，" + fieldName + " 已存在：" + fieldValue));
        }
    }

    /**
     * 校验记录唯一性（主方法）
     *
     * @param monthlySalaryEmployeesDO 待校验的对象
     */
    public void validateUnique(MonthlySalaryEmployeesDO monthlySalaryEmployeesDO) {
        if(monthlySalaryEmployeesDO==null){
            throw new ServiceException(new ErrorCode(2_100_004,"进行唯一校验失败，传入对象不能为空"));
        }

        // 获取当前记录的 ID（新增时为 null 或 0，修改时为有效 ID）
        Long id = monthlySalaryEmployeesDO.getId();

        // 校验手机号唯一性
        validateFieldUnique("phone_number","手机号码", monthlySalaryEmployeesDO.getPhoneNumber(), id);

        //校验手机号有效性
        if (StringUtils.isNotBlank(monthlySalaryEmployeesDO.getPhoneNumber())) {
            boolean isPhone = PhoneUtil.isPhone(monthlySalaryEmployeesDO.getPhoneNumber());
            if(!isPhone){
                throw new ServiceException(new ErrorCode(2_100_004,"手机号格式不正确"));
            }
        }

        // 校验身份证号唯一性
        validateFieldUnique("id_card_number", "身份证号", monthlySalaryEmployeesDO.getIdCardNumber(), id);

        // 校验档案编号唯一性
        validateFieldUnique("archive_number", "档案编号", monthlySalaryEmployeesDO.getArchiveNumber(), id);

        // 校验驾驶证档案编号唯一性（仅当字段不为空时校验）
        if (StringUtils.isNotBlank(monthlySalaryEmployeesDO.getDriverLicenseArchiveNumber())) {
            validateFieldUnique("driver_license_archive_number","驾驶证档案编号", monthlySalaryEmployeesDO.getDriverLicenseArchiveNumber(), id);
        }

        //校验当前人员是否在周薪人员花名册中存在
        if(StringUtils.isNotBlank(monthlySalaryEmployeesDO.getIdCardNumber())){
            Long  personInfoDo = personInfoMapper.selectCount(new QueryWrapperX<PersonInfoDO>().eq("identity", monthlySalaryEmployeesDO.getIdCardNumber()));
            if(personInfoDo>=1){
                    throw new ServiceException(new ErrorCode(2_100_004,"周薪人员花名册中已存在该身份证号：" + monthlySalaryEmployeesDO.getIdCardNumber()));
            }
        }
        if(StringUtils.isNotBlank(monthlySalaryEmployeesDO.getPhoneNumber())){
            Long  personInfoDo = personInfoMapper.selectCount(new QueryWrapperX<PersonInfoDO>().eq("mobile", monthlySalaryEmployeesDO.getPhoneNumber()));
            if(personInfoDo>=1){
                throw new ServiceException(new ErrorCode(2_100_004,"周薪人员花名册中已存在该手机号：" + monthlySalaryEmployeesDO.getPhoneNumber()));
            }
        }
    }

    @Override
    public MonthlySalaryEmployeesRespVO getMonthlySalaryEmployees(Long id) {
        MonthlySalaryEmployeesDO monthlySalaryEmployeesDO = monthlySalaryEmployeesMapper.selectById(id);
        MonthlySalaryEmployeesRespVO monthlySalaryEmployeesRespVO = BeanUtils.toBean(monthlySalaryEmployeesDO, MonthlySalaryEmployeesRespVO.class);
        if(monthlySalaryEmployeesDO!=null){
                 List<MonthlySalaryPersonAttachmentDO> monthlySalaryPersonAttachmentDOList = monthlySalaryPersonAttachmentMapper.selectPersonAttachByPersonId(id);
                monthlySalaryEmployeesRespVO.setPersonAttachmentList(monthlySalaryPersonAttachmentDOList);
        }
        return monthlySalaryEmployeesRespVO;
    }

    @Override
    public PageResult<MonthlySalaryEmployeesRespVO> getMonthlySalaryEmployeesPage(MonthlySalaryEmployeesPageReqVO pageReqVO) {
        if(pageReqVO.getStatus()==null){
            pageReqVO.setStatus(OaConstant.EMPLOYEE_STATUS_DUTY);//默认查在职的人员
        }
        PageResult<MonthlySalaryEmployeesRespVO> monthlySalaryEmployeesDOPageResult = BeanUtils.toBean(monthlySalaryEmployeesMapper.selectPage(pageReqVO), MonthlySalaryEmployeesRespVO.class);
        List<DeptDO> deptDOList = deptMapper.selectList();
        List<PostDO> postDOList = postMapper.selectList();
        monthlySalaryEmployeesDOPageResult.getList().forEach(monthlySalaryEmployeesDO -> {
            deptDOList.stream().filter(dept -> dept.getId().equals(monthlySalaryEmployeesDO.getDeptId())).findFirst().ifPresent(dept->monthlySalaryEmployeesDO.setDeptName(dept.getName()));
            postDOList.stream().filter(post -> post.getId().equals(monthlySalaryEmployeesDO.getPostId())).findFirst().ifPresent(post->monthlySalaryEmployeesDO.setPostName(post.getName()));
            if(monthlySalaryEmployeesDO.getDateOfBirth()!=null){
                //计算年龄 - 使用当前年份减去出生年份
                int birthYear = monthlySalaryEmployeesDO.getDateOfBirth().getYear();
                int currentYear = LocalDate.now().getYear();
                monthlySalaryEmployeesDO.setAge(currentYear - birthYear);
            }
        });
        return monthlySalaryEmployeesDOPageResult;
    }

    @Override
    public PageResult<EmployeesRespVO> getEmployeesPage(MonthlySalaryEmployeesPageReqVO pageReqVO) {
        IPage<EmployeesRespVO> iPage = monthlySalaryEmployeesMapper.getEmployeesPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO);
        PageResult<EmployeesRespVO> employeesRespVOList= new PageResult<>(iPage.getRecords(), iPage.getTotal());
        employeesRespVOList.getList().forEach(person->{
            if(StringUtils.isNotEmpty(person.getDateOfBirth())){
                try {
                    person.setAge(DateUtil.ageOfNow(person.getDateOfBirth()));
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
            if(person.getSex() !=null){
                person.setSexName(person.getSex() == 2 ? "女":"男");
            }
        });
        return employeesRespVOList;
    }

    /**
     * 导入月工资花名册
     *
     * @param list          导入数据列表
     * @param updateSupport 是否支持更新
     * @return 导入结果
     */
    @Override
    @Transactional(rollbackFor=Exception.class)
    public MonthlySalaryEmployeesExcelVO importMonthlySalaryEmployeesList(List<MonthlySalaryEmployeesExcelVO> list, Boolean updateSupport) {
        if(CollectionUtil.isEmpty(list)){
            throw exception(NOT_IMPORT_DATA);
        }
        MonthlySalaryEmployeesExcelVO excelImportRespVo = MonthlySalaryEmployeesExcelVO.builder().createUsernames(new ArrayList<>())
                .updateUsernames(new ArrayList<>()).failureUsernames(new LinkedHashMap<>()).build();
        int index =1;
        //操作导入的对象
        for (MonthlySalaryEmployeesExcelVO excelVO : list) {
            try {
                if(StringUtils.isBlank(excelVO.getName())){
                    throw new ServiceException(new ErrorCode(2_100_004,  "第"+index+"行," + "的姓名不能为空"));
                }
                if(StringUtils.isBlank(excelVO.getDeptName())){
                    throw new ServiceException(new ErrorCode(2_100_004,  "第"+index+"行," + "的部门名称不能为空"));
                }
                //根据岗位名称，部门名称找到其对应的 ID
                Long l = deptMapper.selectCount(new QueryWrapperX<DeptDO>().eq("name", excelVO.getDeptName()));
                if(l == 0){
                    throw new ServiceException(new ErrorCode(2_100_004,  excelVO. getName() + "的部门不存在，请检查！"));
                }
                if(l>1){
                    throw new ServiceException(new ErrorCode(2_100_004,  excelVO.getName() + "的部门存在多个，请检查！"));
                }
                DeptDO dept = deptMapper.selectOne(new QueryWrapperX<DeptDO>().eq("name", excelVO.getDeptName()));
                excelVO.setDeptId(dept.getId());

               if (StringUtils.isBlank(excelVO.getPostName())){
                   throw new ServiceException(new ErrorCode(2_100_004,  "第"+index+"行," + "的岗位名称不能为空"));
               }
                l = postMapper.selectCount(new QueryWrapperX<PostDO>().eq("name", excelVO.getPostName()));
                if(l == 0){
                    throw new ServiceException(new ErrorCode(2_100_004,  excelVO. getName() + "的岗位不存在，请检查！"));
                }
                if(l>1){
                    throw new ServiceException(new ErrorCode(2_100_004,  excelVO.getName() + "的岗位存在多个，请检查！"));
                }
                PostDO post = postMapper.selectOne(new QueryWrapperX<PostDO>().eq("name", excelVO.getPostName()));
                excelVO.setPostId(post.getId());

                //将 Excel 中的长期转换成 2999年 12月 31日
                if(StringUtils.isNotBlank(excelVO.getIdCardExpiryDateEnd()) && excelVO.getIdCardExpiryDateEnd().contains("长期")){
                    excelVO.setIdCardExpiryDateEnd(String.valueOf(LocalDate.of(2999, 12, 31)));
                }
                if(StringUtils.isNotBlank(excelVO.getDriverLicenseExpiryDateEnd()) && excelVO.getDriverLicenseExpiryDateEnd().contains("长期")){
                    excelVO.setDriverLicenseExpiryDateEnd(String.valueOf(LocalDate.of(2999, 12, 31)));
                }
                if(StringUtils.isNotBlank(excelVO.getDriverLicenseRenewalDateEnd()) && excelVO.getDriverLicenseRenewalDateEnd().contains("长期")){
                    excelVO.setDriverLicenseRenewalDateEnd(String.valueOf(LocalDate.of(2999, 12, 31)));
                }
                // 合同期限字段不再是必填项，可能为空
                if(StringUtils.isNotBlank(excelVO.getContractStartDate())){
                    if(excelVO.getContractStartDate().contains("长期")){
                        excelVO.setContractStartDate(String.valueOf(LocalDate.of(2999, 12, 31)));
                    }
                }
                if(StringUtils.isNotBlank(excelVO.getContractEndDate())){
                    if(excelVO.getContractEndDate().contains("长期")){
                        excelVO.setContractEndDate(String.valueOf(LocalDate.of(2999, 12, 31)));
                    }
                }
                //判断用户是否存在，如果存在，判断是否允许更新
                MonthlySalaryEmployeesDO existEmployee = monthlySalaryEmployeesMapper.selectByIdCardNumber(excelVO.getIdCardNumber());
                if(existEmployee != null){
                    //如果不允许更新，则抛出异常
                    if(!updateSupport){
                        throw new ServiceException(new ErrorCode(2_100_004,  excelVO.getName() + " 已存在，且未勾选导入更新选项，不允许操作！"));
                    }else {
                        //更新用户
                        excelVO.setId(existEmployee.getId());
                        MonthlySalaryEmployeesSaveReqVO saveReqVO = convertExcelVOToSaveReqVO(excelVO);
                        this.updateMonthlySalaryEmployees(saveReqVO);
                        excelImportRespVo.getUpdateUsernames().add(excelVO.getName());
                    }
                }else {
                    //为空则新增用户
                    MonthlySalaryEmployeesSaveReqVO saveReqVO = convertExcelVOToSaveReqVO(excelVO);
                    this.createMonthlySalaryEmployees(saveReqVO);
                    excelImportRespVo.getCreateUsernames().add(excelVO.getName());
                }
            }catch (ServiceException e){
                excelImportRespVo.getFailureUsernames().put(excelVO.getName(),e.getMessage());
            }
            index++;
        }
        return excelImportRespVo;
    }

    /**
     * 将Excel实体类转换为保存请求VO
     * 主要处理日期字段的转换
     */
    private MonthlySalaryEmployeesSaveReqVO convertExcelVOToSaveReqVO(MonthlySalaryEmployeesExcelVO excelVO) {
        MonthlySalaryEmployeesSaveReqVO saveReqVO = BeanUtils.toBean(excelVO, MonthlySalaryEmployeesSaveReqVO.class);

        // 处理入职日期
        if (StringUtils.isNotBlank(excelVO.getStartDate())) {
            saveReqVO.setStartDate(LocalDate.parse(excelVO.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }

        // 处理出生日期
        if (StringUtils.isNotBlank(excelVO.getDateOfBirth())) {
            saveReqVO.setDateOfBirth(excelVO.getDateOfBirth());
        }

        // 处理合同期限起
        if (StringUtils.isNotBlank(excelVO.getContractStartDate())) {
            saveReqVO.setContractStartDate(excelVO.getContractStartDate());
        }

        // 处理合同期限止
        if (StringUtils.isNotBlank(excelVO.getContractEndDate())) {
            saveReqVO.setContractEndDate(excelVO.getContractEndDate());
        }

        // 处理身份证有效期限起
        if (StringUtils.isNotBlank(excelVO.getIdCardExpiryDateStart())) {
            saveReqVO.setIdCardExpiryDateStart(excelVO.getIdCardExpiryDateStart());
        }

        // 处理身份证有效期限止
        if (StringUtils.isNotBlank(excelVO.getIdCardExpiryDateEnd())) {
            saveReqVO.setIdCardExpiryDateEnd(excelVO.getIdCardExpiryDateEnd());
        }

        // 处理驾驶证相关日期字段
        if (StringUtils.isNotBlank(excelVO.getDriverLicenseExpiryDateStart())) {
            saveReqVO.setDriverLicenseExpiryDateStart(excelVO.getDriverLicenseExpiryDateStart());
        }

        if (StringUtils.isNotBlank(excelVO.getDriverLicenseExpiryDateEnd())) {
            saveReqVO.setDriverLicenseExpiryDateEnd(excelVO.getDriverLicenseExpiryDateEnd());
        }

        if (StringUtils.isNotBlank(excelVO.getInitialLicenseIssueDate())) {
            saveReqVO.setInitialLicenseIssueDate(excelVO.getInitialLicenseIssueDate());
        }

        // 处理驾驶证审验时限起
        if (StringUtils.isNotBlank(excelVO.getDriverLicenseRenewalDateStart())) {
            saveReqVO.setDriverLicenseRenewalDateStart(excelVO.getDriverLicenseRenewalDateStart());
        }

        // 处理驾驶证审验时限止
        if (StringUtils.isNotBlank(excelVO.getDriverLicenseRenewalDateEnd())) {
            saveReqVO.setDriverLicenseRenewalDateEnd(excelVO.getDriverLicenseRenewalDateEnd());
        }

        // 处理校车驾驶资格取得时间
        if (StringUtils.isNotBlank(excelVO.getSchoolBusQualificationDate())) {
            saveReqVO.setSchoolBusQualificationDate(excelVO.getSchoolBusQualificationDate());
        }

        return saveReqVO;
    }

    /**
     * 手动实现分页，下拉用
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<MonthlySalaryEmployeesPageReqVO> getMonthlySalaryEmployeesCustomPage(MonthlySalaryEmployeesPageReqVO pageReqVO) {
        //自定义实现分页
        IPage<MonthlySalaryEmployeesPageReqVO> iPage = monthlySalaryEmployeesMapper.selectCustomPage(new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize()), pageReqVO);

        PageResult<MonthlySalaryEmployeesPageReqVO> pageResult = new PageResult<>(iPage.getRecords(), iPage.getTotal());

        return pageResult;
    }

    @Override
    public MonthlySalaryEmployeesDO getMonthlySalaryEmployeesByPhone(String phoneNumber) {
        return monthlySalaryEmployeesMapper.selectByPhoneNumber(phoneNumber);
    }

    /**
     * 判断员工岗位类型
     *
     * @param postId 岗位ID
     * @return 岗位类型枚举，包含岗位编码和名称
     */
    @Override
    public PostTypeEnum getPostTypeByPostId(Long postId) {
        if (postId == null) {
            return PostTypeEnum.OFFICE_STAFF; // 默认为办公室人员
        }

        // 查询岗位信息
        PostDO postDO = postMapper.selectById(postId);
        if (postDO == null) {
            log.warn("[判断岗位类型] 未找到岗位ID为 {} 的岗位信息", postId);
            return PostTypeEnum.OFFICE_STAFF; // 默认为办公室人员
        }

        // 获取岗位编码
        String postCode = postDO.getCode();

        // 根据岗位编码判断岗位类型
        if ("captain".equals(postCode)) {
            return PostTypeEnum.CAPTAIN; // 队长
        } else if ("mentor".equals(postCode)) {
            return PostTypeEnum.MENTOR; // 指导师
        } else if ("maintenance".equals(postCode)) {
            return PostTypeEnum.MAINTENANCE; // 维修员
        } else {
            return PostTypeEnum.OFFICE_STAFF; // 办公室人员
        }
    }

    /**
     * 判断员工是否为办公室人员
     *
     * @param postId 岗位ID
     * @return 是否为办公室人员
     */
    @Override
    public boolean isOfficeStaff(Long postId) {
        return getPostTypeByPostId(postId) == PostTypeEnum.OFFICE_STAFF;
    }

    /**
     * 获取岗位编码
     *
     * @param postId 岗位ID
     * @return 岗位编码
     */
    @Override
    public String getPostCode(Long postId) {
        if (postId == null) {
            return null;
        }
        PostDO postDO = postMapper.selectById(postId);
        return postDO != null ? postDO.getCode() : null;
    }

    @Override
    public List<String> getAssignedLocationByLike(String assignedLocation) {
        List<String> assignedLocations = monthlySalaryEmployeesMapper.selectAssignedLocationLike(assignedLocation);
        List<String> collect = assignedLocations.stream().distinct().collect(Collectors.toList());
        return collect;
    }

    @Override
    public List<String> getCompanyByLike(String companyName) {
        List<String> companyNames = monthlySalaryEmployeesMapper.selectCompanyLike(companyName);
        List<String> collect = companyNames.stream().distinct().collect(Collectors.toList());
        return collect;
    }
}
