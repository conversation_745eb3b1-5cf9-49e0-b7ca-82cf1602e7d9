package cn.iocoder.yudao.module.oa.dal.dataobject.fuelreports;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 油耗日月报 DO
 *
 * <AUTHOR>
 */
@TableName("t_fuel_reports")
@KeySequence("t_fuel_reports_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FuelReportsDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 车牌号
     */
    private String vehildno;
    /**
     * 车辆所属公司
     */
    private String companyName;
    /**
     * 日期（时间戳）
     */
    private Long date;
    /**
     * 行驶里程，单位米
     */
    private Long licheng;
    /**
     * 油耗；100 = 1升
     */
    private Integer youHao;
    /**
     * 加油；100 = 1升
     */
    private Integer ayou;
    /**
     * 漏油；100 = 1升
     */
    private Integer ryou;
    /**
     * 是否是月报表
     */
    private Boolean isMonth;

}