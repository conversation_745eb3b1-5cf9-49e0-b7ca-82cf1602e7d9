package cn.iocoder.yudao.module.oa.dal.mysql.buschecked;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.oa.controller.admin.buschecked.vo.BusCheckedPageReqVO;
import cn.iocoder.yudao.module.oa.dal.dataobject.buschecked.BusCheckedDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 车辆事项检查 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BusCheckedMapper extends BaseMapperX<BusCheckedDO> {

    default PageResult<BusCheckedDO> selectPage(BusCheckedPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BusCheckedDO>()
                .likeIfPresent(BusCheckedDO::getCheckedName, reqVO.getCheckedName())
                .eqIfPresent(BusCheckedDO::getBusPhoto, reqVO.getBusPhoto())
                .eqIfPresent(BusCheckedDO::getCheckedPeo, reqVO.getCheckedPeo())
                .eqIfPresent(BusCheckedDO::getTripId, reqVO.getTripId())
                .betweenIfPresent(BusCheckedDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BusCheckedDO::getId));
    }


}
