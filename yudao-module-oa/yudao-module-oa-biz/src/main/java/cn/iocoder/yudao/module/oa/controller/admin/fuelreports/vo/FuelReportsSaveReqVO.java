package cn.iocoder.yudao.module.oa.controller.admin.fuelreports.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 油耗日月报新增/修改 Request VO")
@Data
public class FuelReportsSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5804")
    private Long id;

    @Schema(description = "车牌号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "车牌号不能为空")
    private String vehildno;

    @Schema(description = "车辆所属公司", example = "王五")
    private String companyName;

    @Schema(description = "设备上报日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date businessDate;

    @Schema(description = "行驶里程，单位米")
    private Long licheng;

    @Schema(description = "油耗/升")
    private Integer youHao;

    @Schema(description = "加油/升")
    private Integer ayou;

    @Schema(description = "漏油/升")
    private Integer ryou;

    @Schema(description = "是否是月报表")
    private Boolean isMonth;

}