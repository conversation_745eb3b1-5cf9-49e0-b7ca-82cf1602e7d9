package cn.iocoder.yudao.module.oa.service.fuelreports;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.oa.controller.admin.fuelreports.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.fuelreports.FuelReportsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 油耗日月报 Service 接口
 *
 * <AUTHOR>
 */
public interface FuelReportsService {

    /**
     * 创建油耗日月报
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createFuelReports(@Valid FuelReportsSaveReqVO createReqVO);

    /**
     * 更新油耗日月报
     *
     * @param updateReqVO 更新信息
     */
    void updateFuelReports(@Valid FuelReportsSaveReqVO updateReqVO);

    /**
     * 删除油耗日月报
     *
     * @param id 编号
     */
    void deleteFuelReports(Long id);

    /**
     * 获得油耗日月报
     *
     * @param id 编号
     * @return 油耗日月报
     */
    FuelReportsDO getFuelReports(Long id);

    /**
     * 获得油耗日月报分页
     *
     * @param pageReqVO 分页查询
     * @return 油耗日月报分页
     */
    PageResult<FuelReportsDO> getFuelReportsPage(FuelReportsPageReqVO pageReqVO);

    /**
     * 同步油耗日月报数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param vehiIds   车牌号（多个用逗号分隔）
     * @param isMonth   是否月报
     * @return 同步结果信息
     */
    String syncFuelReportsData(String startTime, String endTime, String vehiIds, Boolean isMonth);

}