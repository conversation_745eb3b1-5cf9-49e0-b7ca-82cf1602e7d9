package cn.iocoder.yudao.module.oa.service.socialsecurity;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.oa.controller.admin.socialsecurity.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.socialsecurity.SocialSecurityDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 社保管理 Service 接口
 *
 * <AUTHOR>
 */
public interface SocialSecurityService {

    /**
     * 创建社保管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createSocialSecurity(@Valid SocialSecuritySaveReqVO createReqVO);

    /**
     * 更新社保管理
     *
     * @param updateReqVO 更新信息
     */
    void updateSocialSecurity(@Valid SocialSecuritySaveReqVO updateReqVO);

    /**
     * 删除社保管理
     *
     * @param id 编号
     */
    void deleteSocialSecurity(Integer id);

    /**
     * 获得社保管理
     *
     * @param id 编号
     * @return 社保管理
     */
    SocialSecurityDO getSocialSecurity(Integer id);

    /**
     * 获得社保管理分页
     *
     * @param pageReqVO 分页查询
     * @return 社保管理分页
     */
    PageResult<SocialSecurityRespVO> getSocialSecurityPage(SocialSecurityPageReqVO pageReqVO);

    SocialSecurityImportRespVO importBusTripList(List<SocialSecurityImportRespVO> list,  Boolean updateSupport);

    List<SocialSecurityRespVO> getSocialSecurityByPostId(Integer personId);
}
