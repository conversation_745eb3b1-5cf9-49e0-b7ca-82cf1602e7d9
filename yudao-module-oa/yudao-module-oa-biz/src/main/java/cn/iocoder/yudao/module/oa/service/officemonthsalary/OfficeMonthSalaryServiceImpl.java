package cn.iocoder.yudao.module.oa.service.officemonthsalary;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.mybatis.core.query.QueryWrapperX;
import cn.iocoder.yudao.module.oa.commons.constants.OaConstant;
import cn.iocoder.yudao.module.oa.controller.admin.monthlysalaryemployees.vo.MonthlySalaryEmployeesPageReqVO;
import cn.iocoder.yudao.module.oa.dal.dataobject.ferrydutycost.FerryDutyCostDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.housingfund.HousingFundDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.incometax.IncomeTaxDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.monthlyattendance.MonthlyAttendanceDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.monthlysalaryemployees.MonthlySalaryEmployeesDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.officestaffsalary.OfficeStaffSalaryDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.oilsubsidy.OilSubsidyDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.otherfee.OtherFeeDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.overtimesubsidy.OvertimeSubsidyDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.salarydeduct.SalaryDeductDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.socialsecurity.SocialSecurityDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.weeksalary.WeekSalaryDO;
import cn.iocoder.yudao.module.oa.dal.mysql.ferrydutycost.FerryDutyCostMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.housingfund.HousingFundMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.incometax.IncomeTaxMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.monthlyattendance.MonthlyAttendanceMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.monthlysalaryemployees.MonthlySalaryEmployeesMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.officestaffsalary.OfficeStaffSalaryMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.oilsubsidy.OilSubsidyMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.otherfee.OtherFeeMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.overtimesubsidy.OvertimeSubsidyMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.salarydeduct.SalaryDeductMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.socialsecurity.SocialSecurityMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.weeksalary.WeekSalaryMapper;
import cn.iocoder.yudao.module.oa.util.CheckUtil;
import cn.iocoder.yudao.module.system.controller.admin.dept.vo.post.PostPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.PostDO;
import cn.iocoder.yudao.module.system.dal.mysql.dept.PostMapper;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import cn.iocoder.yudao.module.oa.controller.admin.officemonthsalary.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.officemonthsalary.OfficeMonthSalaryDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.oa.dal.mysql.officemonthsalary.OfficeMonthSalaryMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.*;

/**
 * 办公室员工工资 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OfficeMonthSalaryServiceImpl implements OfficeMonthSalaryService {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    //静态锁对象，用于控制并发
    private static final Object CALCULATION_LOCK = new Object();
    //记录当前是否有正在计算薪资的任务在执行
    private static volatile boolean calculationInProgress = false;
    //记录计算任务开始时间
    private static volatile long calculationStartTime = 0;

    @Resource
    private OfficeMonthSalaryMapper officeMonthSalaryMapper;

    @Resource
    private MonthlySalaryEmployeesMapper monthlySalaryEmployeesMapper; //月薪花名册表
    @Resource
    private PostMapper postMapper; //职务表
    @Resource
    private OfficeStaffSalaryMapper officeStaffSalaryMapper;
    @Resource
    private MonthlyAttendanceMapper monthlyAttendanceMapper; //月薪考勤表
    @Resource
    private OilSubsidyMapper oilSubsidyMapper; //油费补贴表
    @Resource
    private OtherFeeMapper otherFeeMapper; //其他费用表
    @Resource
    private OvertimeSubsidyMapper overtimeSubsidyMapper; //加班表
    @Resource
    private FerryDutyCostMapper ferryDutyCostMapper; //摆渡费用表
    @Resource
    private SalaryDeductMapper salaryDeductMapper; //薪酬扣减表
    @Resource
    private SocialSecurityMapper socialSecurityMapper; //社保表
    @Resource
    private HousingFundMapper housingFundMapper; //公积金费用表
    @Resource
    private IncomeTaxMapper incomeTaxMapper; //个税表
    @Resource
    private WeekSalaryMapper weekSalaryMapper;

    @Override
    public Long createOfficeMonthSalary(OfficeMonthSalarySaveReqVO createReqVO) {
        // 插入
        OfficeMonthSalaryDO officeMonthSalary = BeanUtils.toBean(createReqVO, OfficeMonthSalaryDO.class);
        officeMonthSalaryMapper.insert(officeMonthSalary);
        // 返回
        return officeMonthSalary.getId();
    }

    @Override
    public void updateOfficeMonthSalary(OfficeMonthSalarySaveReqVO updateReqVO) {
        // 校验存在
        validateOfficeMonthSalaryExists(updateReqVO.getId());
        // 更新
        OfficeMonthSalaryDO updateObj = BeanUtils.toBean(updateReqVO, OfficeMonthSalaryDO.class);
        officeMonthSalaryMapper.updateById(updateObj);
    }

    @Override
    public void deleteOfficeMonthSalary(Long id) {
        // 校验存在
        validateOfficeMonthSalaryExists(id);
        // 删除
        officeMonthSalaryMapper.deleteById(id);
    }

    private void validateOfficeMonthSalaryExists(Long id) {
        if (officeMonthSalaryMapper.selectById(id) == null) {
            throw exception(OFFICE_MONTH_SALARY_NOT_EXISTS);
        }
    }

    @Override
    public OfficeMonthSalaryDO getOfficeMonthSalary(Long id) {
        return officeMonthSalaryMapper.selectById(id);
    }

    @Override
    public PageResult<OfficeMonthSalaryDO> getOfficeMonthSalaryPage(OfficeMonthSalaryPageReqVO pageReqVO) {
        return officeMonthSalaryMapper.selectPage(pageReqVO);
    }

    /**
     * 构建并插入月薪数据
     *
     * @param personIds
     * @param yearMonth
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OfficeMonthSalaryRespVO caluateOfficeMonth(String yearMonth, List<Long> personIds) {
        synchronized (CALCULATION_LOCK) {
            if (calculationInProgress) {
                // 如果已经有一个计算任务在执行，判断是否执行超过 30分钟
                long currentTime = System.currentTimeMillis();
                if (currentTime - calculationStartTime < 30 * 60 * 1000) {
                    throw new ServiceException(new ErrorCode(2_100_005, "已有一个计算办公室人员月薪的任务正在执行，请稍后再试"));
                } else {
                    // 如果任务执行超过30分钟，认为之前的任务可能已经异常，重置状态
                    logger.warn("上一个计算任务执行时间超过30分钟，可能已经异常，重置状态");
                    calculationInProgress = false;
                }
            }
            //标记计算任务开始
            calculationInProgress = true;
            calculationStartTime = System.currentTimeMillis();
        }
        try {
            String needMonth = CheckUtil.checkYearMonth(yearMonth, logger);
            List<Long> needPersonIds = new ArrayList<>();

            //构建结果集
            OfficeMonthSalaryRespVO respVO = OfficeMonthSalaryRespVO.builder().createUsernames(new ArrayList<>())
                    .failureUsernames(new LinkedHashMap<>()).build();

            // 查询所有岗位，排除司机(normaldriver/ferry)、跟车老师(normalfollowcar/follow)、第一负责人（fstresp）、队长（captain）、指导师（mentor）、（维修员（maintenance）、副技术总监(depcto)：维修公司），其他职务都是办公室人员
            List<PostDO> list = postMapper.selectList(new QueryWrapperX<PostDO>());
            List<Long> officeStaffList = getPostIdList(list);

            if (CollectionUtil.isEmpty(personIds)) {
                if (officeStaffList.isEmpty()) {
                    throw new ServiceException(new ErrorCode(2_100_004, "未在职务表找到办公室员工的职务信息，无需生成月薪"));
                }

                List<MonthlySalaryEmployeesDO> monthlySalaryEmployeesDOS = monthlySalaryEmployeesMapper.selectList(MonthlySalaryEmployeesDO::getPostId, officeStaffList);
                //如果都为空则抛出未找到合适的人员异常
                if (CollectionUtil.isEmpty(monthlySalaryEmployeesDOS)) {
                    throw new ServiceException(new ErrorCode(2_100_004, "未在月薪人员表找到职务为：办公室员工的人员信息，无需生成月薪"));
                }
                for (MonthlySalaryEmployeesDO monthlySalaryEmployeesDO : monthlySalaryEmployeesDOS) {
                    needPersonIds.add(monthlySalaryEmployeesDO.getId());
                }
            } else {
                needPersonIds = personIds;
            }
            if (CollectionUtil.isEmpty(needPersonIds)) {
                throw new ServiceException(new ErrorCode(2_100_004, "未在月薪花名册中找到办公室员工的人员信息，无需生成月薪"));
            }
            //遍历needPersonIds，查询出对应的人员信息，并计算出工资
            for (Long needPersonId : needPersonIds) {
                try {
                    this.handleMonthlySalary(needPersonId, needMonth);
                    respVO.getCreateUsernames().add(needPersonId.toString());
                } catch (Exception e) {
                    logger.info(e.getMessage());
                    respVO.getFailureUsernames().put(needPersonId.toString(), e.getMessage());
                }
            }
            return respVO;
        } finally {
            //无论如何都要重置状态
            synchronized (CALCULATION_LOCK) {
                calculationInProgress = false;
            }
        }
    }

    private static @NotNull List<Long> getPostIdList(List<PostDO> list) {
        List<Long> officeStaffList = new ArrayList<>();

        for (PostDO postDO : list) {
            String postType = postDO.getCode();
            if (!postType.contains("normaldriver") && !postType.contains("ferry") &&
                    !postType.contains("normalfollowcar") && !postType.contains("follow") &&
                    !postType.contains("fstresp") && !postType.contains("captain") &&
                    !postType.contains("mentor") && !postType.contains("maintenance") &&
                    !postType.contains("depcto")) {
                officeStaffList.add(postDO.getId());
            }
        }
        return officeStaffList;
    }


    /**
     * 计算月薪
     *
     * @param personId
     * @param yearMonth
     * @return
     * @throws Exception
     */
    public OfficeMonthSalaryRespVO handleMonthlySalary(Long personId, String yearMonth) throws Exception {
        //根据传来的 yyyy-mm 计算其在这月的开始和结束时间，因为有的基础数据表是精确到日的
        Date yearMonthDate = DateUtil.parse(yearMonth, "yyyy-MM");

        DateTime endOfMonthDateTime = DateUtil.endOfMonth(yearMonthDate);

        //将yyyy-MM转换为 yyyy年mm月（周薪审核表那边的存储要求）
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM");
        // 解析输入字符串
        inputFormat.setLenient(false); // 确保严格匹配格式
        inputFormat.parse(yearMonth); // 验证输入是否合法
        // 定义输出格式
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy年MM月");
        String yearMonthFormatter = outputFormat.format(inputFormat.parse(yearMonth));

        String startOfMonthStr = yearMonth + "-01";
        String endOfMonthStr = endOfMonthDateTime.toString("yyyy-MM-dd");

        OfficeMonthSalaryRespVO respVO = new OfficeMonthSalaryRespVO();
        MonthlySalaryEmployeesPageReqVO info = monthlySalaryEmployeesMapper.selectCustomByPersonId(personId);
        if (Objects.isNull(info)) {
            throw new ServiceException(new ErrorCode(2_100_004, "未在月薪花名册中找到personId为" + personId + "的人员信息，生成月薪失败"));
        }
        respVO.setCompanyName(info.getCompanyName());
        respVO.setDepartmentName(info.getDeptId().toString());
        respVO.setPostName(info.getPostName());
        respVO.setPostCode(String.valueOf(info.getPostId()));
        respVO.setPersonId(personId);
        respVO.setEmployeeName(info.getName());
        respVO.setIdNumber(info.getIdCardNumber());
        respVO.setPhone(info.getPhoneNumber());
        respVO.setBankName(info.getBankBranch());
        respVO.setBankCard(info.getBankAccountNumber());

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        respVO.setEntryDate(LocalDate.parse(info.getStartDate(), formatter));

        // 查询薪酬表，拿到岗位工资+职务工资+全勤奖+月度绩效+工龄+餐补+住宿+交通费+油补
        OfficeStaffSalaryDO officeSalaryDO = officeStaffSalaryMapper.selectOne(OfficeStaffSalaryDO::getPersonId, personId);
        if (officeSalaryDO == null) {
            throw new ServiceException(new ErrorCode(2_100_004, "未在办公室薪酬表中找到人员" + info.getName() + "的信息，生成月薪失败"));
        }
        respVO.setPostSalary(officeSalaryDO.getPostWage() == null ? BigDecimal.ZERO : officeSalaryDO.getPostWage());
        respVO.setDutySalary(officeSalaryDO.getJobSalary() == null ? BigDecimal.ZERO : officeSalaryDO.getJobSalary());
        respVO.setAttendanceBonus(officeSalaryDO.getAttendanceAward() == null ? BigDecimal.ZERO : officeSalaryDO.getAttendanceAward());
        respVO.setMonthlyBonus(officeSalaryDO.getMonthPerformance() == null ? BigDecimal.ZERO : officeSalaryDO.getMonthPerformance());
        respVO.setSenioritySubsidy(officeSalaryDO.getSeniorityAward() == null ? BigDecimal.ZERO : officeSalaryDO.getSeniorityAward());
        respVO.setMealSubsidy(officeSalaryDO.getMealsFee() == null ? BigDecimal.ZERO : officeSalaryDO.getMealsFee());
        respVO.setHousingSubsidy(officeSalaryDO.getHousingFee() == null ? BigDecimal.ZERO : officeSalaryDO.getHousingFee());
        respVO.setTransportSubsidy(officeSalaryDO.getTravelFee() == null ? BigDecimal.ZERO : officeSalaryDO.getTravelFee());

        //油费补贴
        OilSubsidyDO oilSubsidyDO = oilSubsidyMapper.selectOne(OilSubsidyDO::getPersonId, personId, OilSubsidyDO::getOccurrenceTime, yearMonth);
        if (oilSubsidyDO != null) {
            respVO.setGasSubsidy(oilSubsidyDO.getOilSubsidyFee() == null ? BigDecimal.ZERO : oilSubsidyDO.getOilSubsidyFee());
        } else {
            respVO.setGasSubsidy(BigDecimal.ZERO);
        }

        //查询考勤表，计算出出勤天数
        MonthlyAttendanceDO monthlyAttendanceDOS = monthlyAttendanceMapper.selectOne(MonthlyAttendanceDO::getPersonId, personId,
                MonthlyAttendanceDO::getOccurrenceTime, yearMonth);
        if (monthlyAttendanceDOS == null) {
            throw new ServiceException(new ErrorCode(2_100_004, "未在月薪考勤表中找到人员" + info.getName() + "的信息，生成月薪失败"));
        }

        // 出勤天数
        respVO.setAttendanceDays(monthlyAttendanceDOS.getAttendanceDay() == null ? BigDecimal.ZERO : monthlyAttendanceDOS.getAttendanceDay());
        // 缺勤天数
        respVO.setAbsenceDays(monthlyAttendanceDOS.getAbsenceDay() == null ? BigDecimal.ZERO : monthlyAttendanceDOS.getAbsenceDay());
        // 总天数
        BigDecimal totalDays = monthlyAttendanceDOS.getAttendanceTotalDay().add(monthlyAttendanceDOS.getAbsenceDay());

        //其他费用
        List<OtherFeeDO> otherFeeDOS = otherFeeMapper.selectList(new QueryWrapperX<OtherFeeDO>().eq("drive_phone", respVO.getPhone())
                .between("deduct_date", startOfMonthStr, endOfMonthStr)
                .eq("type", OaConstant.SALARY_CATEGORY_MONTHLY));//根据手机号码找到这个人在这个月内的其他费用
        //如果不为空，汇总fee
        if (!CollectionUtil.isEmpty(otherFeeDOS)) {
            //汇总
            BigDecimal otherExpenses = otherFeeDOS.stream().map(OtherFeeDO::getFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            respVO.setOtherExpenses(otherExpenses);
        } else {
            respVO.setOtherExpenses(BigDecimal.ZERO);
        }

        //正常加班费
        OvertimeSubsidyDO overtimeSubsidyDOS = overtimeSubsidyMapper.selectOne(OvertimeSubsidyDO::getPersonId, personId, OvertimeSubsidyDO::getOccurrenceTime, yearMonth);
        //如果不为空，汇总overtimePay
        if (overtimeSubsidyDOS != null) {
            //加班费用由本月补贴和加班费组成
            BigDecimal totalOverTimeFee = overtimeSubsidyDOS.getOvertimePay() == null ? BigDecimal.ZERO : overtimeSubsidyDOS.getOvertimePay().add(overtimeSubsidyDOS.getSubsidy() == null ? BigDecimal.ZERO : overtimeSubsidyDOS.getSubsidy());
            respVO.setNormalOvertime(totalOverTimeFee);
        } else {
            respVO.setNormalOvertime(BigDecimal.ZERO);
        }
        if(respVO.getNormalOvertime().compareTo(BigDecimal.ZERO) > 0){
            respVO.setNormalOvertime(respVO.getNormalOvertime().setScale(0, RoundingMode.HALF_UP));
        }

        //摆渡加班费=摆渡费用登记表+周摆渡费用表
        BigDecimal totalWeekFerryCost = BigDecimal.ZERO;
        List<WeekSalaryDO>weekSalaryDOS = weekSalaryMapper.selectList(new QueryWrapperX<WeekSalaryDO>().eq("mobile", info.getPhoneNumber())
                .like("week", yearMonthFormatter));
        if(!CollectionUtil.isEmpty(weekSalaryDOS)){
            //汇总费用作为周摆渡费
            for (WeekSalaryDO weekSalaryDO : weekSalaryDOS) {
                totalWeekFerryCost = totalWeekFerryCost.add(weekSalaryDO.getSalary()==null?BigDecimal.ZERO:weekSalaryDO.getSalary());
            }
        }
        //摆渡值班费用登记表
        FerryDutyCostDO ferryDutyCostDO = ferryDutyCostMapper.selectOne(FerryDutyCostDO::getPersonId, personId, FerryDutyCostDO::getOccurrenceTime, yearMonth);
        if (ferryDutyCostDO != null) {
            respVO.setFerryOvertime(ferryDutyCostDO.getTotalFee() == null ? BigDecimal.ZERO : ferryDutyCostDO.getTotalFee());
        } else {
            respVO.setFerryOvertime(BigDecimal.ZERO);
        }
        respVO.setFerryOvertime(respVO.getFerryOvertime().add(totalWeekFerryCost));

        //加班费用总计=正常加班费+摆渡加班费
        respVO.setTotalOvertime(respVO.getNormalOvertime().add(respVO.getFerryOvertime()));

        if(respVO.getTotalOvertime().compareTo(BigDecimal.ZERO) > 0){
            respVO.setTotalOvertime(respVO.getTotalOvertime().setScale(0, RoundingMode.HALF_UP));
        }

        //缺勤扣款=（岗位工资+职务工资+全勤奖+月度绩效+工龄+餐补+住宿+交通费）/26*缺勤天数
        BigDecimal absenceDeduction = BigDecimal.ZERO;
        try {
            absenceDeduction = respVO.getPostSalary()
                    .add(respVO.getDutySalary())
                    .add(respVO.getAttendanceBonus())
                    .add(respVO.getMonthlyBonus())
                    .add(respVO.getSenioritySubsidy())
                    .add(respVO.getMealSubsidy())
                    .add(respVO.getHousingSubsidy())
                    .add(respVO.getTransportSubsidy());
            absenceDeduction = absenceDeduction.divide(totalDays, 2, RoundingMode.HALF_UP);
            absenceDeduction = absenceDeduction.multiply(respVO.getAbsenceDays());
        } catch (Exception e) {
            throw new ServiceException(new ErrorCode(2_100_004, "月薪计算失败,生成" + info.getName() + ",缺勤扣款失败"));
        }
        respVO.setAbsenceDeduction(absenceDeduction);
        if(respVO.getAbsenceDeduction().compareTo(BigDecimal.ZERO) > 0){
            respVO.setAbsenceDeduction(respVO.getAbsenceDeduction().setScale(0, RoundingMode.HALF_UP));
        }

        //其他扣款
        List<SalaryDeductDO> salaryDeductDOS = salaryDeductMapper.selectList(new QueryWrapperX<SalaryDeductDO>().eq("drive_phone", respVO.getPhone())
                .between("deduct_date", startOfMonthStr, endOfMonthStr)
                .eq("type", OaConstant.SALARY_CATEGORY_MONTHLY));
        if (!CollectionUtil.isEmpty(salaryDeductDOS)) {
            //汇总
            BigDecimal totalDeductFee = salaryDeductDOS.stream().map(SalaryDeductDO::getDeductFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            respVO.setOtherDeduction(totalDeductFee);
        } else {
            respVO.setOtherDeduction(BigDecimal.ZERO);
        }
        //扣款小计=缺勤扣款+其他扣款
        respVO.setTotalDeduction(respVO.getAbsenceDeduction().add(respVO.getOtherDeduction()));
        if(respVO.getTotalDeduction().compareTo(BigDecimal.ZERO) > 0){
            respVO.setTotalDeduction(respVO.getTotalDeduction().setScale(0, RoundingMode.HALF_UP));
        }

        //应发工资=（岗位工资+职务工资+全勤奖+月度绩效+工龄+餐补+住宿+交通费）/应出勤天数*出勤天数汇总+"油补"+“其他”+“加班工资小计”-“缺勤工资小计”
        BigDecimal shouldPaySalary = BigDecimal.ZERO;
        try {
            shouldPaySalary = respVO.getPostSalary()
                    .add(respVO.getDutySalary())
                    .add(respVO.getAttendanceBonus())
                    .add(respVO.getMonthlyBonus())
                    .add(respVO.getSenioritySubsidy())
                    .add(respVO.getMealSubsidy())
                    .add(respVO.getHousingSubsidy())
                    .add(respVO.getTransportSubsidy());
            shouldPaySalary = shouldPaySalary.divide(totalDays, 2, RoundingMode.HALF_UP);
            shouldPaySalary = shouldPaySalary.multiply(monthlyAttendanceDOS.getAttendanceTotalDay());
            shouldPaySalary = shouldPaySalary.add(respVO.getGasSubsidy())
                    .add(respVO.getOtherExpenses())
                    .add(respVO.getTotalOvertime())
                    .subtract(respVO.getOtherDeduction());
        } catch (Exception e) {
            throw new ServiceException(new ErrorCode(2_100_004, "月薪计算失败,生成" + info.getName() + ",应发工资失败"));
        }
        respVO.setGrossSalary(shouldPaySalary);
        if(respVO.getGrossSalary().compareTo(BigDecimal.ZERO) > 0){
            //对应发工资进行四舍五入
            respVO.setGrossSalary(respVO.getGrossSalary().setScale(0, RoundingMode.HALF_UP));
        }

        // ==========================公司代缴项=================================

        //社保=社保表
        SocialSecurityDO socialSecurityDO = socialSecurityMapper.selectOne(new QueryWrapperX<SocialSecurityDO>().eq("person_id", personId));
        if (socialSecurityDO != null) {
            respVO.setSocialInsurance(socialSecurityDO.getPersonTotalFee() == null ? BigDecimal.ZERO : socialSecurityDO.getPersonTotalFee());
        } else {
            respVO.setSocialInsurance(BigDecimal.ZERO);
        }
        //公积金=公积金表
        HousingFundDO housingFundDO = housingFundMapper.selectOne(HousingFundDO::getPersonId, personId);
        if (housingFundDO != null) {
            respVO.setHousingFund(housingFundDO.getIndividualDepositAmount() == null ? BigDecimal.ZERO : housingFundDO.getIndividualDepositAmount());
        } else {
            respVO.setHousingFund(BigDecimal.ZERO);
        }
        //社保公积金总额=社保+公积金
        respVO.setTotalInsurance(respVO.getSocialInsurance().add(respVO.getHousingFund()));

        if(respVO.getTotalInsurance().compareTo(BigDecimal.ZERO)>0){
            //对社保公积金总额进行四舍五入
            respVO.setTotalInsurance(respVO.getTotalInsurance().setScale(0, RoundingMode.HALF_UP));
        }

        //税前工资=应发工资-社保公积金总额
        respVO.setPreTaxSalary(respVO.getGrossSalary().subtract(respVO.getTotalInsurance()));

        if(respVO.getPreTaxSalary().compareTo(BigDecimal.ZERO) > 0){
            //对应税前工资进行四舍五入
            respVO.setPreTaxSalary(respVO.getPreTaxSalary().setScale(0, RoundingMode.HALF_UP));
        }

        //个人所得税=个税表
        IncomeTaxDO incomeTaxDO = incomeTaxMapper.selectOne(IncomeTaxDO::getPersonId, personId);
        if (incomeTaxDO != null) {
            respVO.setIncomeTax(incomeTaxDO.getTaxAmount() == null ? BigDecimal.ZERO : incomeTaxDO.getTaxAmount());
        } else {
            respVO.setIncomeTax(BigDecimal.ZERO);
        }

        //实发工资=税前工资-个人所得税
        respVO.setNetSalary(respVO.getPreTaxSalary().subtract(respVO.getIncomeTax()));
        if (respVO.getNetSalary().compareTo(BigDecimal.ZERO) > 0) {
            //对应实发工资进行四舍五入
            respVO.setNetSalary(respVO.getNetSalary().setScale(0, RoundingMode.HALF_UP));
        }

        //设置薪资所在月份
        respVO.setOccurrenceTime(yearMonth);
        //计算完成后插入到数据库中，如果存在就先删除
        OfficeMonthSalaryDO officeMonthSalaryDO = officeMonthSalaryMapper.selectOne(OfficeMonthSalaryDO::getPersonId, personId,
                OfficeMonthSalaryDO::getOccurrenceTime, yearMonth);
        if (Objects.nonNull(officeMonthSalaryDO)) {
            officeMonthSalaryMapper.deleteById(officeMonthSalaryDO.getId());
        }
        int insert = officeMonthSalaryMapper.insert(BeanUtils.toBean(respVO, OfficeMonthSalaryDO.class));
        if (insert > 0) {
            return respVO;
        } else {
            throw new ServiceException(new ErrorCode(2_100_004, "月薪计算失败,生成" + info.getName() + ",插入失败"));
        }
    }

}