package cn.iocoder.yudao.module.oa.dal.mysql.vehiclerecords;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.oa.dal.dataobject.vehiclerecords.VehicleRecordsDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.oa.controller.admin.vehiclerecords.vo.*;

/**
 * 油量汇总 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface VehicleRecordsMapper extends BaseMapperX<VehicleRecordsDO> {

    default PageResult<VehicleRecordsDO> selectPage(VehicleRecordsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VehicleRecordsDO>()
                .eqIfPresent(VehicleRecordsDO::getDriver, reqVO.getDriver())
                .likeIfPresent(VehicleRecordsDO::getVehildno, reqVO.getVehildno())
                .likeIfPresent(VehicleRecordsDO::getCompanyName, reqVO.getCompanyName())
                .geIfPresent(VehicleRecordsDO::getBeginTime, reqVO.getBeginTime())
                .leIfPresent(VehicleRecordsDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(VehicleRecordsDO::getStartYouLiang, reqVO.getStartYouLiang())
                .eqIfPresent(VehicleRecordsDO::getEndYouLiang, reqVO.getEndYouLiang())
                .eqIfPresent(VehicleRecordsDO::getLiCheng, reqVO.getLiCheng())
                .eqIfPresent(VehicleRecordsDO::getYouLiang, reqVO.getYouLiang())
                .eqIfPresent(VehicleRecordsDO::getAddYouLiang, reqVO.getAddYouLiang())
                .betweenIfPresent(VehicleRecordsDO::getWorkTime, reqVO.getWorkTime())
                .betweenIfPresent(VehicleRecordsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(VehicleRecordsDO::getId));
    }

}