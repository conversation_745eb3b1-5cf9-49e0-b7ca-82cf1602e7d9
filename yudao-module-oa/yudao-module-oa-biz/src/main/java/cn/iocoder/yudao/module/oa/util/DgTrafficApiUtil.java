package cn.iocoder.yudao.module.oa.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.oa.dal.dataobject.fuelconsumptioncurve.FuelConsumptionCurveDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.fuelreports.FuelReportsDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.vehiclerecords.VehicleRecordsDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 通天星交通安全平台API工具类
 */
@Component
@Slf4j
public class DgTrafficApiUtil {

    @Value("${dgtraffic.domain}")
    private String domain;

    @Autowired
    private DgTrafficLoginUtil loginUtil;

    // API路径常量
    private static final String OIL_SUMMARY_PATH = "StandardApiAction_oilSummary.action";
    private static final String OIL_TRACK_DETAIL_PATH = "StandardApiAction_oilTrackDetail.action";
    private static final String OIL_DAY_MONTH_DATA_PATH = "StandardApiAction_getOilDayMonthData.action";

    // 默认超时时间（毫秒）
    private static final int DEFAULT_TIMEOUT = 30000;

    // 默认每页记录数
    private static final int DEFAULT_PAGE_SIZE = 10;

    // 默认坐标系类型（百度坐标系）
    private static final String DEFAULT_TO_MAP = "2";

    /**
     * 获取油耗汇总数据
     *
     * @param beginTime 开始时间（格式：yyyy-MM-dd）
     * @param endTime   结束时间（格式：yyyy-MM-dd）
     * @param vehiIds   车牌号列表（可为空，多个车牌用英文逗号分隔）
     * @param pageSize  每页记录数（默认10）
     * @param toMap     坐标系类型（0:WGS84,1:gj02,2:bd09）
     * @param timeout   请求超时时间（毫秒）
     * @return 油耗汇总数据列表
     */
    public List<VehicleRecordsDO> getOilSummaryData(String beginTime, String endTime,
                                                 List<String> vehiIds, Integer pageSize,
                                                 String toMap, Integer timeout)throws Exception {
        // 获取有效的会话
        DgTrafficLoginUtil.LoginResult session = loginUtil.getValidSession();
        String jsession = session.getJsession();

        // 设置默认值
        int actualPageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
        String actualToMap = StrUtil.isNotBlank(toMap) ? toMap : DEFAULT_TO_MAP;
        int actualTimeout = timeout != null ? timeout : DEFAULT_TIMEOUT;

        // 获取所有分页数据
        List<JSONObject> allData = fetchAllOilSummaryData(jsession, beginTime, endTime,
                                                         actualToMap, vehiIds, actualPageSize, actualTimeout);

        // 转换为业务对象
        return convertToOilSummaryInfoList(allData);
    }

    /**
     * 获取油量统计所有分页数据
     *
     * @param jsession   会话ID
     * @param beginTime  开始时间（格式：yyyy-MM-dd）
     * @param endTime    结束时间（格式：yyyy-MM-dd）
     * @param toMap      坐标系类型（0:WGS84,1:gj02,2:bd09）
     * @param vehiIds    车牌号列表（可为空，多个车牌用英文逗号分隔）
     * @param pageSize   每页记录数
     * @param timeout    请求超时时间（毫秒）
     * @return 所有数据集合
     */
    private List<JSONObject> fetchAllOilSummaryData(String jsession, String beginTime, String endTime,
                                                  String toMap, List<String> vehiIds, int pageSize, int timeout) throws Exception{
        List<JSONObject> allData = new ArrayList<>();
        int currentPage = 1;
        int totalPages = 1;

        // 转换车牌号格式（URL编码处理）
        String encodedVehIds = CollUtil.isNotEmpty(vehiIds)
                ? HttpUtil.encodeParams(String.join(",", vehiIds), StandardCharsets.UTF_8)
                : null;

        do {
            try {
                // 构建请求URL
                String url = StrUtil.format("{}{}?jsession={}&begintime={}&endtime={}&toMap={}&pageRecords={}&currentPage={}",
                        domain, OIL_SUMMARY_PATH, jsession, beginTime, endTime, toMap, pageSize, currentPage);

                // 添加可选的车牌号参数
                if (StrUtil.isNotBlank(encodedVehIds)) {
                    url += "&vehidno=" + encodedVehIds;
                }

                log.warn("Requesting oil summary data, URL: {}", url);

                // 发送请求
                HttpResponse response = HttpRequest.get(url)
                        .timeout(timeout)
                        .execute();

                // 解析响应
                String responseBody = response.body();
                JSONObject jsonResponse = JSONUtil.parseObj(responseBody);

                // 检查响应状态
                int result = jsonResponse.getInt("result", -1);
                if (result != 0) {
                    log.error("Failed to fetch oil summary data, result code: {}, response: {}", result, responseBody);
                    throw new RuntimeException("Failed to fetch oil summary data, result code: " + result);
                }

                // 提取当前页数据
                JSONArray infosArray = jsonResponse.getJSONArray("infos");
                if (infosArray != null) {
                    List<JSONObject> currentPageData = infosArray.toList(JSONObject.class);
                    allData.addAll(currentPageData);
                }

                // 更新分页信息
                JSONObject pagination = jsonResponse.getJSONObject("pagination");
                if (pagination != null) {
                    totalPages = pagination.getInt("totalPages", 1);
                    currentPage++;
                } else {
                    break;
                }

            } catch (Exception e) {
                log.error("Error fetching oil summary data for page {}", currentPage, e);
                throw new RuntimeException("Error fetching oil summary data: " + e.getMessage(), e);
            }
        } while (currentPage <= totalPages);

        return allData;
    }

    /**
     * 将JSON数据转换为业务对象列表
     *
     * @param jsonDataList JSON数据列表
     * @return 业务对象列表
     */
private List<VehicleRecordsDO> convertToOilSummaryInfoList(List<JSONObject> jsonDataList) throws Exception{
    List<VehicleRecordsDO> result = new ArrayList<>();

    if (jsonDataList == null || jsonDataList.isEmpty()) {
        return result;
    }

    for (JSONObject json : jsonDataList) {
        try {
            VehicleRecordsDO info = new VehicleRecordsDO();
            // 安全获取字符串值，如果为空则提供默认值
            info.setDriver(StrUtil.blankToDefault(json.getStr("driver"), ""));
            info.setVehildno(StrUtil.emptyToDefault(json.getStr("vehildno"), "")); // 车牌号不能为空，但可以是空字符串
            info.setCompanyName(StrUtil.blankToDefault(json.getStr("companyName"), ""));
            info.setBeginTime(DateUtil.parse(json.getStr("bTimeStr"), "yyyy-MM-dd HH:mm:ss"));
            info.setEndTime(DateUtil.parse(json.getStr("eTimeStr"), "yyyy-MM-dd HH:mm:ss"));

            // 使用 BigDecimal.valueOf 更安全
            info.setStartYouLiang(BigDecimal.valueOf(json.getInt("startYouLiang", 0)));
            info.setEndYouLiang(BigDecimal.valueOf(json.getInt("endYouLiang", 0)));
            info.setLiCheng(BigDecimal.valueOf(json.getInt("liCheng", 0)));
            info.setYouLiang(BigDecimal.valueOf(json.getInt("youLiang", 0)));
            info.setAddYouLiang(BigDecimal.valueOf(json.getInt("addYouLiang", 0)));

            // workTime 是 Long 类型
            info.setWorkTime(json.getLong("workTime", 0L));

            result.add(info);
        } catch (Exception e) {
            log.error("转换油耗数据失败: {}", json, e);
            // 可以选择跳过这条记录或者抛出异常
        }
    }

    return result;
}

    /**
     * 获取油量轨迹详情数据
     *
     * @param beginTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime   结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param vehiIdno  车牌号（需要转码），只支持单个车辆查询
     * @param toMap     坐标系类型（0:WGS84,1:gj02,2:bd09）
     * @param distance  轨迹间距(单位m)，可为空
     * @param pageSize  每页记录数（默认10）
     * @param timeout   请求超时时间（毫秒）
     * @return 油量轨迹详情数据列表
     */
    public List<FuelConsumptionCurveDO> getOilTrackDetailData(String beginTime, String endTime,
                                                              String vehiIdno, String toMap,
                                                              String distance, Integer pageSize,
                                                              Integer timeout) throws Exception {
        // 参数验证
        if (StrUtil.isBlank(vehiIdno)) {
            throw new IllegalArgumentException("车牌号不能为空");
        }

        // 获取有效的会话
        DgTrafficLoginUtil.LoginResult session = loginUtil.getValidSession();
        String jsession = session.getJsession();

        // 设置默认值
        int actualPageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
        String actualToMap = StrUtil.isNotBlank(toMap) ? toMap : DEFAULT_TO_MAP;
        int actualTimeout = timeout != null ? timeout : DEFAULT_TIMEOUT;

        // 获取所有分页数据
        List<JSONObject> allData = fetchAllOilTrackDetailData(jsession, beginTime, endTime,
                                                             vehiIdno, actualToMap, distance,
                                                             actualPageSize, actualTimeout);

        // 转换为业务对象
        return convertToOilTrackDetailList(allData);
    }

    /**
     * 获取油量轨迹详情所有分页数据
     *
     * @param jsession   会话ID
     * @param beginTime  开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime    结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param vehiIdno   车牌号（需要转码），只支持单个车辆查询
     * @param toMap      坐标系类型（0:WGS84,1:gj02,2:bd09）
     * @param distance   轨迹间距(单位m)，可为空
     * @param pageSize   每页记录数
     * @param timeout    请求超时时间（毫秒）
     * @return 所有数据集合
     */
    private List<JSONObject> fetchAllOilTrackDetailData(String jsession, String beginTime, String endTime,
                                                        String vehiIdno, String toMap, String distance,
                                                        int pageSize, int timeout) throws Exception {
        List<JSONObject> allData = new ArrayList<>();
        int currentPage = 1;
        int totalPages = 1;

        // 车牌号URL编码处理
        String encodedVehiIdno = HttpUtil.encodeParams(vehiIdno, StandardCharsets.UTF_8);

        do {
            try {
                // 构建请求URL
                String url = StrUtil.format("{}{}?jsession={}&begintime={}&endtime={}&vehiIdno={}&pageRecords={}&currentPage={}",
                        domain, OIL_TRACK_DETAIL_PATH, jsession, beginTime, endTime,
                        encodedVehiIdno, pageSize, currentPage);

                // 添加可选的轨迹间距参数
                if (StrUtil.isNotBlank(distance)) {
                    url += "&distance=" + distance;
                }

                if (StrUtil.isNotBlank(toMap)) {
                    url += "&toMap=" + toMap;
                }

                log.warn("Requesting oil track detail data, URL: {}", url);

                // 发送请求
                HttpResponse response = HttpRequest.get(url)
                        .timeout(timeout)
                        .execute();

                // 解析响应
                String responseBody = response.body();
                JSONObject jsonResponse = JSONUtil.parseObj(responseBody);

                // 检查响应状态
                int result = jsonResponse.getInt("result", -1);
                if (result != 0) {
                    log.error("Failed to fetch oil track detail data, result code: {}, response: {}", result, responseBody);
                    throw new RuntimeException("Failed to fetch oil track detail data, result code: " + result);
                }

                // 提取当前页数据
                JSONArray infosArray = jsonResponse.getJSONArray("infos");
                if (infosArray != null) {
                    List<JSONObject> currentPageData = infosArray.toList(JSONObject.class);
                    allData.addAll(currentPageData);
                }

                // 更新分页信息
                JSONObject pagination = jsonResponse.getJSONObject("pagination");
                if (pagination != null) {
                    totalPages = pagination.getInt("totalPages", 1);
                    currentPage++;
                } else {
                    break;
                }

            } catch (Exception e) {
                log.error("Error fetching oil track detail data for page {}", currentPage, e);
                throw new RuntimeException("Error fetching oil track detail data: " + e.getMessage(), e);
            }
        } while (currentPage <= totalPages);

        return allData;
    }

    /**
     * 将JSON数据转换为油量轨迹详情业务对象列表
     *
     * @param jsonDataList JSON数据列表
     * @return 业务对象列表
     */
    private List<FuelConsumptionCurveDO> convertToOilTrackDetailList(List<JSONObject> jsonDataList) throws Exception {
        List<FuelConsumptionCurveDO> result = new ArrayList<>();

        if (jsonDataList == null || jsonDataList.isEmpty()) {
            return result;
        }

        for (JSONObject json : jsonDataList) {
            try {
                FuelConsumptionCurveDO detail = new FuelConsumptionCurveDO();

                // 安全获取字符串值，如果为空则提供默认值
                detail.setVehildno(StrUtil.emptyToDefault(json.getStr("vehildno"), ""));
                detail.setCompanyName(StrUtil.blankToDefault(json.getStr("companyName"), ""));
                detail.setGpsTimeStr(StrUtil.blankToDefault(json.getStr("gpsTimeStr"), ""));
                detail.setPosition(StrUtil.blankToDefault(json.getStr("position"), ""));
                detail.setSpeed(new BigDecimal(StrUtil.blankToDefault(json.getStr("speed"), "0")));

                // 安全获取数值类型
                detail.setYouLiang(BigDecimal.valueOf(json.getInt("youLiang", 0)));
                detail.setViceYouLiang(BigDecimal.valueOf(json.getInt("viceYouLiang", 0)));
                detail.setLiCheng(BigDecimal.valueOf(json.getInt("liCheng", 0)));

                result.add(detail);
            } catch (Exception e) {
                log.error("转换油量轨迹详情数据失败: {}", json, e);
                // 可以选择跳过这条记录或者抛出异常
            }
        }

        return result;
    }

    /**
     * 获取油耗日月报数据
     *
     * @param beginTime 开始时间（格式：yyyy-MM-dd 或 yyyy-MM）
     * @param endTime   结束时间（格式：yyyy-MM-dd 或 yyyy-MM）
     * @param vehiIds   车牌号列表（可为空，多个车牌用英文逗号分隔）
     * @param isMonth   是否按月查询（true：按月查询，false：按日查询）
     * @param pageSize  每页记录数（默认10）
     * @param timeout   请求超时时间（毫秒）
     * @return 油耗日月报数据列表
     */
    public List<FuelReportsDO> getOilDayMonthData(String beginTime, String endTime,
                                                  List<String> vehiIds, Boolean isMonth,
                                                  Integer pageSize, Integer timeout) throws Exception {
        // 获取有效的会话
        DgTrafficLoginUtil.LoginResult session = loginUtil.getValidSession();
        String jsession = session.getJsession();

        // 设置默认值
        int actualPageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
        int actualTimeout = timeout != null ? timeout : DEFAULT_TIMEOUT;
        boolean actualIsMonth = isMonth != null ? isMonth : false;

        // 获取所有分页数据
        List<JSONObject> allData = fetchAllOilDayMonthData(jsession, beginTime, endTime,
                                                          vehiIds, actualIsMonth, actualPageSize, actualTimeout);

        // 转换为业务对象
        return convertToFuelReportsList(allData, actualIsMonth);
    }

    /**
     * 获取油耗日月报所有分页数据
     *
     * @param jsession   会话ID
     * @param beginTime  开始时间（格式：yyyy-MM-dd 或 yyyy-MM）
     * @param endTime    结束时间（格式：yyyy-MM-dd 或 yyyy-MM）
     * @param vehiIds    车牌号列表（可为空，多个车牌用英文逗号分隔）
     * @param isMonth    是否按月查询
     * @param pageSize   每页记录数
     * @param timeout    请求超时时间（毫秒）
     * @return 所有数据集合
     */
    private List<JSONObject> fetchAllOilDayMonthData(String jsession, String beginTime, String endTime,
                                                    List<String> vehiIds, boolean isMonth,
                                                    int pageSize, int timeout) throws Exception {
        List<JSONObject> allData = new ArrayList<>();
        int currentPage = 1;
        int totalPages = 1;

        // 转换车牌号格式（URL编码处理）
        String encodedVehIds = CollUtil.isNotEmpty(vehiIds)
                ? HttpUtil.encodeParams(String.join(",", vehiIds), StandardCharsets.UTF_8)
                : null;

        do {
            try {
                // 构建请求URL
                String url = StrUtil.format("{}{}?jsession={}&begintime={}&endtime={}",
                        domain, OIL_DAY_MONTH_DATA_PATH, jsession, beginTime, endTime);

                // 添加可选的车牌号参数
                if (StrUtil.isNotBlank(encodedVehIds)) {
                    url += "&vehiIdno=" + encodedVehIds;
                }

                // 添加月查询参数
                if (isMonth) {
                    url += "&isMonth=true";
                }

                // 添加分页参数
                url += "&pageRecords=" + pageSize + "&currentPage=" + currentPage;

                log.warn("Requesting oil day/month data, URL: {}", url);

                // 发送请求
                HttpResponse response = HttpRequest.get(url)
                        .timeout(timeout)
                        .execute();

                // 解析响应
                String responseBody = response.body();
                JSONObject jsonResponse = JSONUtil.parseObj(responseBody);

                // 检查响应状态
                int result = jsonResponse.getInt("result", -1);
                if (result != 0) {
                    log.error("Failed to fetch oil day/month data, result code: {}, response: {}", result, responseBody);
                    throw new RuntimeException("Failed to fetch oil day/month data, result code: " + result);
                }

                // 提取当前页数据
                JSONArray infosArray = jsonResponse.getJSONArray("infos");
                if (infosArray != null) {
                    List<JSONObject> currentPageData = infosArray.toList(JSONObject.class);
                    allData.addAll(currentPageData);
                }

                // 更新分页信息
                JSONObject pagination = jsonResponse.getJSONObject("pagination");
                if (pagination != null) {
                    totalPages = pagination.getInt("totalPages", 1);
                    currentPage++;
                } else {
                    break;
                }

            } catch (Exception e) {
                log.error("Error fetching oil day/month data for page {}", currentPage, e);
                throw new RuntimeException("Error fetching oil day/month data: " + e.getMessage(), e);
            }
        } while (currentPage <= totalPages);

        return allData;
    }

    /**
     * 将JSON数据转换为油耗日月报业务对象列表
     *
     * @param jsonDataList JSON数据列表
     * @param isMonth      是否为月报数据
     * @return 业务对象列表
     */
    private List<FuelReportsDO> convertToFuelReportsList(List<JSONObject> jsonDataList, boolean isMonth) throws Exception {
        List<FuelReportsDO> result = new ArrayList<>();

        if (jsonDataList == null || jsonDataList.isEmpty()) {
            return result;
        }

        for (JSONObject json : jsonDataList) {
            try {
                FuelReportsDO report = new FuelReportsDO();

                // 安全获取字符串值，如果为空则提供默认值
                report.setVehildno(StrUtil.emptyToDefault(json.getStr("vehiIdno"), ""));
                report.setCompanyName(StrUtil.blankToDefault(json.getStr("companyName"), ""));

                // 获取日期时间戳
                Long dateTimestamp = json.getLong("date");
                if (dateTimestamp != null) {
                  //将时间戳转换为日期时间
                    report.setBusinessDate(new Date(dateTimestamp));
                }

                // 获取里程数据（单位：米）
                Long licheng = json.getLong("licheng");
                if (licheng != null) {
                    report.setLicheng(licheng);
                }

                // 获取油耗相关数据（100 = 1升）
                Integer youHao = json.getInt("youHao");
                if (youHao != null) {
                    report.setYouHao(youHao);
                }

                Integer ayou = json.getInt("ayou");
                if (ayou != null) {
                    BigDecimal ayou = BigDecimal.valueOf(ayou).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                    report.setAyou(ayou);
                }

                Integer ryou = json.getInt("ryou");
                if (ryou != null) {
                    report.setRyou(ryou);
                }

                // 设置是否为月报
                report.setIsMonth(isMonth);

                result.add(report);
            } catch (Exception e) {
                log.error("转换油耗日月报数据失败: {}", json, e);
                // 可以选择跳过这条记录或者抛出异常
            }
        }

        return result;
    }

}
