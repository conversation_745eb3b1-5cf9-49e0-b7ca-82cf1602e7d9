package cn.iocoder.yudao.module.oa.controller.admin.fuelreports.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 油耗日月报分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FuelReportsPageReqVO extends PageParam {

    @Schema(description = "车牌号")
    private String vehildno;

    @Schema(description = "车辆所属公司", example = "王五")
    private String companyName;

    @Schema(description = "设备上报日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] businessDate;

    @Schema(description = "行驶里程，单位米")
    private Long licheng;

    @Schema(description = "油耗；100 = 1升")
    private Integer youHao;

    @Schema(description = "加油；100 = 1升")
    private Integer ayou;

    @Schema(description = "漏油；100 = 1升")
    private Integer ryou;

    @Schema(description = "是否是月报表")
    private Boolean isMonth;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}