package cn.iocoder.yudao.module.oa.service.fuelreports;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.oa.util.DgTrafficApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.oa.controller.admin.fuelreports.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.fuelreports.FuelReportsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.oa.dal.mysql.fuelreports.FuelReportsMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.*;

/**
 * 油耗日月报 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class FuelReportsServiceImpl implements FuelReportsService {

    @Resource
    private FuelReportsMapper fuelReportsMapper;

    @Resource
    private DgTrafficApiUtil dgTrafficApiUtil;

    @Override
    public Long createFuelReports(FuelReportsSaveReqVO createReqVO) {
        // 插入
        FuelReportsDO fuelReports = BeanUtils.toBean(createReqVO, FuelReportsDO.class);
        fuelReportsMapper.insert(fuelReports);
        // 返回
        return fuelReports.getId();
    }

    @Override
    public void updateFuelReports(FuelReportsSaveReqVO updateReqVO) {
        // 校验存在
        validateFuelReportsExists(updateReqVO.getId());
        // 更新
        FuelReportsDO updateObj = BeanUtils.toBean(updateReqVO, FuelReportsDO.class);
        fuelReportsMapper.updateById(updateObj);
    }

    @Override
    public void deleteFuelReports(Long id) {
        // 校验存在
        validateFuelReportsExists(id);
        // 删除
        fuelReportsMapper.deleteById(id);
    }

    private void validateFuelReportsExists(Long id) {
        if (fuelReportsMapper.selectById(id) == null) {
            throw exception(FUEL_REPORTS_NOT_EXISTS);
        }
    }

    @Override
    public FuelReportsDO getFuelReports(Long id) {
        return fuelReportsMapper.selectById(id);
    }

    @Override
    public PageResult<FuelReportsDO> getFuelReportsPage(FuelReportsPageReqVO pageReqVO) {
        return fuelReportsMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncFuelReportsData(String startTime, String endTime, String vehiIds, Boolean isMonth) {
        log.info("[syncFuelReportsData][开始同步油耗日月报数据，开始时间：{}，结束时间：{}，车牌号：{}，是否月报：{}]",
                startTime, endTime, StrUtil.isBlank(vehiIds) ? "全部" : vehiIds, isMonth);

        try {
            // 参数校验和默认值设置
            String[] validatedParams = validateAndSetDefaults(startTime, endTime, vehiIds, isMonth);
            String actualStartTime = validatedParams[0];
            String actualEndTime = validatedParams[1];
            String actualVehiIds = validatedParams[2];
            boolean actualIsMonth = Boolean.parseBoolean(validatedParams[3]);

            // 处理车牌号列表
            List<String> vehiIdList = null;
            if (StrUtil.isNotBlank(actualVehiIds)) {
                vehiIdList = StrUtil.split(actualVehiIds, ',', true, true);
            }

            // 分批处理车牌号，每批最多100个
            List<FuelReportsDO> allFuelReports = new ArrayList<>();
            if (CollUtil.isNotEmpty(vehiIdList)) {
                allFuelReports = processBatchVehicles(actualStartTime, actualEndTime, vehiIdList, actualIsMonth);
            } else {
                // 如果没有指定车牌号，直接获取所有数据
                List<FuelReportsDO> fuelReports = dgTrafficApiUtil.getOilDayMonthData(
                        actualStartTime, actualEndTime, null, actualIsMonth, null, null);
                allFuelReports.addAll(fuelReports);
            }

            // 保存数据到数据库
            int savedCount = saveFuelReportsData(allFuelReports);

            String resultMessage = StrUtil.format("同步成功，时间范围：{} 至 {}，获取{}条数据，保存{}条数据",
                    actualStartTime, actualEndTime, allFuelReports.size(), savedCount);
            log.info("[syncFuelReportsData][{}]", resultMessage);
            return resultMessage;
        } catch (Exception e) {
            log.error("[syncFuelReportsData][同步油耗日月报数据异常]", e);
            return "同步失败：" + e.getMessage();
        }
    }

    /**
     * 参数校验和默认值设置
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param vehiIds   车牌号
     * @param isMonth   是否月报
     * @return 校验后的参数数组 [startTime, endTime, vehiIds, isMonth]
     */
    private String[] validateAndSetDefaults(String startTime, String endTime, String vehiIds, Boolean isMonth) {
        String actualStartTime = StrUtil.blankToDefault(startTime, "");
        String actualEndTime = StrUtil.blankToDefault(endTime, "");
        String actualVehiIds = StrUtil.blankToDefault(vehiIds, "");
        boolean actualIsMonth = isMonth != null ? isMonth : false;

        // 如果开始时间和结束时间都为空，则使用昨天的日期
        if (StrUtil.isBlank(actualStartTime) && StrUtil.isBlank(actualEndTime)) {
            String yesterday = DateUtil.formatDate(DateUtil.yesterday());
            actualStartTime = yesterday;
            actualEndTime = yesterday;
            log.info("[validateAndSetDefaults][未指定日期范围，默认使用昨天：{}]", yesterday);
        }

        // 简单的日期格式校验
        if (StrUtil.isNotBlank(actualStartTime)) {
            try {
                if (actualIsMonth) {
                    // 月份格式校验 yyyy-MM
                    if (!actualStartTime.matches("\\d{4}-\\d{2}")) {
                        throw new IllegalArgumentException("月份格式错误，应为 yyyy-MM 格式");
                    }
                } else {
                    // 日期格式校验 yyyy-MM-dd
                    if (!actualStartTime.matches("\\d{4}-\\d{2}-\\d{2}")) {
                        throw new IllegalArgumentException("日期格式错误，应为 yyyy-MM-dd 格式");
                    }
                }
            } catch (Exception e) {
                throw new IllegalArgumentException("开始时间格式错误：" + e.getMessage());
            }
        }

        if (StrUtil.isNotBlank(actualEndTime)) {
            try {
                if (actualIsMonth) {
                    // 月份格式校验 yyyy-MM
                    if (!actualEndTime.matches("\\d{4}-\\d{2}")) {
                        throw new IllegalArgumentException("月份格式错误，应为 yyyy-MM 格式");
                    }
                } else {
                    // 日期格式校验 yyyy-MM-dd
                    if (!actualEndTime.matches("\\d{4}-\\d{2}-\\d{2}")) {
                        throw new IllegalArgumentException("日期格式错误，应为 yyyy-MM-dd 格式");
                    }
                }
            } catch (Exception e) {
                throw new IllegalArgumentException("结束时间格式错误：" + e.getMessage());
            }
        }

        return new String[]{actualStartTime, actualEndTime, actualVehiIds, String.valueOf(actualIsMonth)};
    }

    /**
     * 分批处理车辆数据，每批最多100个车牌号
     *
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param vehiIdList  车牌号列表
     * @param isMonth     是否月报
     * @return 所有批次的数据
     */
    private List<FuelReportsDO> processBatchVehicles(String startTime, String endTime,
                                                    List<String> vehiIdList, boolean isMonth) throws Exception {
        List<FuelReportsDO> allFuelReports = new ArrayList<>();
        int batchSize = 100;
        int totalSize = vehiIdList.size();

        for (int i = 0; i < totalSize; i += batchSize) {
            // 计算当前批次的结束索引
            int endIndex = Math.min(i + batchSize, totalSize);

            // 获取当前批次的车牌号列表
            List<String> batchVehiIds = vehiIdList.subList(i, endIndex);

            log.info("正在获取第 {}/{} 批车辆油耗日月报数据，本批次包含 {} 个车牌",
                     (i / batchSize) + 1,
                     (totalSize + batchSize - 1) / batchSize,
                     batchVehiIds.size());

            // 调用API获取当前批次的数据
            List<FuelReportsDO> batchData = dgTrafficApiUtil.getOilDayMonthData(
                    startTime, endTime, batchVehiIds, isMonth, null, null);

            // 将当前批次的数据添加到总结果中
            if (CollUtil.isNotEmpty(batchData)) {
                allFuelReports.addAll(batchData);
                log.info("成功获取第 {}/{} 批车辆油耗日月报数据，获取到 {} 条记录",
                         (i / batchSize) + 1,
                         (totalSize + batchSize - 1) / batchSize,
                         batchData.size());
            } else {
                log.warn("第 {}/{} 批车辆油耗日月报数据为空",
                         (i / batchSize) + 1,
                         (totalSize + batchSize - 1) / batchSize);
            }

            // 可选：添加一个短暂的延迟，避免频繁请求第三方接口
            try {
                Thread.sleep(500); // 500毫秒延迟
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        return allFuelReports;
    }

    /**
     * 保存油耗日月报数据到数据库
     *
     * @param fuelReportsList 油耗日月报数据列表
     * @return 成功保存的记录数
     */
    private int saveFuelReportsData(List<FuelReportsDO> fuelReportsList) {
        int savedCount = 0;

        for (FuelReportsDO fuelReport : fuelReportsList) {
            try {
                // 这里可以添加业务逻辑，比如检查是否已存在相同数据
                // 可以根据车牌号、日期等字段进行去重判断
                if (shouldSaveFuelReport(fuelReport)) {
                    fuelReportsMapper.insert(fuelReport);
                    savedCount++;
                }
            } catch (Exception e) {
                log.error("[saveFuelReportsData][保存油耗日月报数据失败，车牌号：{}]", fuelReport.getVehildno(), e);
            }
        }

        return savedCount;
    }

    /**
     * 判断是否应该保存油耗日月报数据（去重逻辑）
     *
     * @param fuelReport 油耗日月报数据
     * @return 是否应该保存
     */
    private boolean shouldSaveFuelReport(FuelReportsDO fuelReport) {
        // 这里可以实现去重逻辑，比如根据车牌号和日期查询是否已存在
        // 示例：检查是否已存在相同车牌号和日期的记录
        // 你可以根据实际需求修改这个逻辑

        // 简单的非空校验
        if (StrUtil.isBlank(fuelReport.getVehildno())) {
            log.warn("[shouldSaveFuelReport][车牌号为空，跳过保存]");
            return false;
        }

        if (fuelReport.getDate() == null) {
            log.warn("[shouldSaveFuelReport][日期为空，跳过保存，车牌号：{}]", fuelReport.getVehildno());
            return false;
        }

        // TODO: 这里可以添加更复杂的去重逻辑
        // 例如：查询数据库中是否已存在相同的记录
        // List<FuelReportsDO> existingReports = fuelReportsMapper.selectByVehildnoAndDate(
        //     fuelReport.getVehildno(), fuelReport.getDate());
        // return CollUtil.isEmpty(existingReports);

        return true;
    }

}