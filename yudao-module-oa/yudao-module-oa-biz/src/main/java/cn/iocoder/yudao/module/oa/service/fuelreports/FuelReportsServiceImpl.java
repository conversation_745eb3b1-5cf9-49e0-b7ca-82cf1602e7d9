package cn.iocoder.yudao.module.oa.service.fuelreports;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.oa.controller.admin.fuelreports.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.fuelreports.FuelReportsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.oa.dal.mysql.fuelreports.FuelReportsMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.*;

/**
 * 油耗日月报 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FuelReportsServiceImpl implements FuelReportsService {

    @Resource
    private FuelReportsMapper fuelReportsMapper;

    @Override
    public Long createFuelReports(FuelReportsSaveReqVO createReqVO) {
        // 插入
        FuelReportsDO fuelReports = BeanUtils.toBean(createReqVO, FuelReportsDO.class);
        fuelReportsMapper.insert(fuelReports);
        // 返回
        return fuelReports.getId();
    }

    @Override
    public void updateFuelReports(FuelReportsSaveReqVO updateReqVO) {
        // 校验存在
        validateFuelReportsExists(updateReqVO.getId());
        // 更新
        FuelReportsDO updateObj = BeanUtils.toBean(updateReqVO, FuelReportsDO.class);
        fuelReportsMapper.updateById(updateObj);
    }

    @Override
    public void deleteFuelReports(Long id) {
        // 校验存在
        validateFuelReportsExists(id);
        // 删除
        fuelReportsMapper.deleteById(id);
    }

    private void validateFuelReportsExists(Long id) {
        if (fuelReportsMapper.selectById(id) == null) {
            throw exception(FUEL_REPORTS_NOT_EXISTS);
        }
    }

    @Override
    public FuelReportsDO getFuelReports(Long id) {
        return fuelReportsMapper.selectById(id);
    }

    @Override
    public PageResult<FuelReportsDO> getFuelReportsPage(FuelReportsPageReqVO pageReqVO) {
        return fuelReportsMapper.selectPage(pageReqVO);
    }

}