package cn.iocoder.yudao.module.oa.job.fuel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.oa.dal.dataobject.fuelreports.FuelReportsDO;
import cn.iocoder.yudao.module.oa.service.fuelreports.FuelReportsService;
import cn.iocoder.yudao.module.oa.util.DgTrafficApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 油耗日月报定时任务
 *
 * <AUTHOR>
 */
@Component("FuelReportsJob") // 显式指定Bean名称为"FuelReportsJob"
@Slf4j
public class FuelReportsJob implements JobHandler {

    @Resource
    private DgTrafficApiUtil dgTrafficApiUtil;
    
    @Resource
    private FuelReportsService fuelReportsService;

    @Override
    @TenantJob
    public String execute(String param) throws Exception {
        log.info("[execute][油耗日月报定时任务开始，参数：{}]", param);

        try {
            // 默认值
            String startTime = "";
            String endTime = "";
            String vehiIds = "";
            boolean isMonth = false;
            
            // 如果参数不为空，按逗号分隔获取参数
            if (StrUtil.isNotBlank(param)) {
                String[] params = param.split(",");
                
                // 安全地获取参数，避免数组越界
                if (params.length > 0) {
                    startTime = params[0] == null ? "" : params[0].trim();
                }
                if (params.length > 1) {
                    endTime = params[1] == null ? "" : params[1].trim();
                }
                if (params.length > 2) {
                    vehiIds = params[2] == null ? "" : params[2].trim();
                }
                if (params.length > 3) {
                    String isMonthStr = params[3] == null ? "" : params[3].trim();
                    isMonth = "true".equalsIgnoreCase(isMonthStr) || "1".equals(isMonthStr);
                }
            }
            
            // 如果开始时间和结束时间都为空，则使用昨天的日期
            if (StrUtil.isBlank(startTime) && StrUtil.isBlank(endTime)) {
                String yesterday = DateUtil.formatDate(DateUtil.yesterday());
                startTime = yesterday;
                endTime = yesterday;
                log.info("[execute][未指定日期范围，默认使用昨天：{}]", yesterday);
            }
            
            // 处理车牌号列表
            List<String> vehiIdList = null;
            if (StrUtil.isNotBlank(vehiIds)) {
                vehiIdList = StrUtil.split(vehiIds, ',', true, true);
            }
            
            log.info("[execute][开始获取油耗日月报数据，开始时间：{}，结束时间：{}，车牌号：{}，是否月报：{}]", 
                    startTime, endTime, StrUtil.isBlank(vehiIds) ? "全部" : vehiIds, isMonth);
            
            // 分批处理车牌号，每批最多100个
            List<FuelReportsDO> allFuelReports = new ArrayList<>();
            if (CollUtil.isNotEmpty(vehiIdList)) {
                allFuelReports = processBatchVehicles(startTime, endTime, vehiIdList, isMonth);
            } else {
                // 如果没有指定车牌号，直接获取所有数据
                List<FuelReportsDO> fuelReports = dgTrafficApiUtil.getOilDayMonthData(
                        startTime, endTime, null, isMonth, null, null);
                allFuelReports.addAll(fuelReports);
            }
            
            // 保存数据到数据库
            int savedCount = 0;
            for (FuelReportsDO fuelReport : allFuelReports) {
                try {
                    // 这里可以添加业务逻辑，比如检查是否已存在相同数据
                    // fuelReportsService.createFuelReports(fuelReport);
                    savedCount++;
                } catch (Exception e) {
                    log.error("[execute][保存油耗日月报数据失败，车牌号：{}]", fuelReport.getVehildno(), e);
                }
            }
            
            log.info("[execute][油耗日月报定时任务结束，获取到{}条数据，成功保存{}条]", allFuelReports.size(), savedCount);
            return StrUtil.format("执行成功，时间范围：{} 至 {}，获取{}条数据，保存{}条数据", 
                    startTime, endTime, allFuelReports.size(), savedCount);
        } catch (Exception e) {
            log.error("[execute][油耗日月报定时任务异常]", e);
            return "执行失败：" + e.getMessage();
        }
    }
    
    /**
     * 分批处理车辆数据，每批最多100个车牌号
     *
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param vehiIdList  车牌号列表
     * @param isMonth     是否月报
     * @return 所有批次的数据
     */
    private List<FuelReportsDO> processBatchVehicles(String startTime, String endTime, 
                                                    List<String> vehiIdList, boolean isMonth) throws Exception {
        List<FuelReportsDO> allFuelReports = new ArrayList<>();
        int batchSize = 100;
        int totalSize = vehiIdList.size();
        
        for (int i = 0; i < totalSize; i += batchSize) {
            // 计算当前批次的结束索引
            int endIndex = Math.min(i + batchSize, totalSize);
            
            // 获取当前批次的车牌号列表
            List<String> batchVehiIds = vehiIdList.subList(i, endIndex);
            
            log.info("正在获取第 {}/{} 批车辆油耗日月报数据，本批次包含 {} 个车牌", 
                     (i / batchSize) + 1, 
                     (totalSize + batchSize - 1) / batchSize, 
                     batchVehiIds.size());
            
            // 调用API获取当前批次的数据
            List<FuelReportsDO> batchData = dgTrafficApiUtil.getOilDayMonthData(
                    startTime, endTime, batchVehiIds, isMonth, null, null);
            
            // 将当前批次的数据添加到总结果中
            if (CollUtil.isNotEmpty(batchData)) {
                allFuelReports.addAll(batchData);
                log.info("成功获取第 {}/{} 批车辆油耗日月报数据，获取到 {} 条记录", 
                         (i / batchSize) + 1, 
                         (totalSize + batchSize - 1) / batchSize, 
                         batchData.size());
            } else {
                log.warn("第 {}/{} 批车辆油耗日月报数据为空", 
                         (i / batchSize) + 1, 
                         (totalSize + batchSize - 1) / batchSize);
            }
            
            // 可选：添加一个短暂的延迟，避免频繁请求第三方接口
            try {
                Thread.sleep(500); // 500毫秒延迟
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        return allFuelReports;
    }
}
