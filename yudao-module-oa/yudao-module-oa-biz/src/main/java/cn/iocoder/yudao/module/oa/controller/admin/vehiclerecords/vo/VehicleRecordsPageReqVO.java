package cn.iocoder.yudao.module.oa.controller.admin.vehiclerecords.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 油量汇总分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VehicleRecordsPageReqVO extends PageParam {

    @Schema(description = "司机信息")
    private String driver;

    @Schema(description = "车牌号")
    private String vehildno;

    @Schema(description = "车辆所属公司", example = "李四")
    private String companyName;

    @Schema(description = "开始时间(设备上报数据时间)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;

    @Schema(description = "结束时间(设备上报数据时间)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @Schema(description = "开始油量；100 = 1升")
    private BigDecimal startYouLiang;

    @Schema(description = "结束油量/升")
    private BigDecimal endYouLiang;

    @Schema(description = "总里程，单位米")
    private BigDecimal liCheng;

    @Schema(description = "总油耗/升")
    private BigDecimal youLiang;

    @Schema(description = "加油量/升")
    private BigDecimal addYouLiang;

    @Schema(description = "时长/小时")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] workTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}