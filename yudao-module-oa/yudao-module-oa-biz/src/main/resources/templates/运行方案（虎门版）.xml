<?xml version="1.0"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook
    xmlns="urn:schemas-microsoft-com:office:spreadsheet"
    xmlns:o="urn:schemas-microsoft-com:office:office"
    xmlns:x="urn:schemas-microsoft-com:office:excel"
    xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882"
    xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
    xmlns:html="http://www.w3.org/TR/REC-html40">
    <CustomDocumentProperties
        xmlns="urn:schemas-microsoft-com:office:office">
        <KSOProductBuildVer dt:dt="string">2052-12.1.0.20784</KSOProductBuildVer>
        <ICV dt:dt="string">FEDFDCAF8AFA4026A959EF4351A4AD52_13</ICV>
    </CustomDocumentProperties>
    <OfficeDocumentSettings
        xmlns="urn:schemas-microsoft-com:office:office">
        <AllowPNG/>
    </OfficeDocumentSettings>
    <ExcelWorkbook
        xmlns="urn:schemas-microsoft-com:office:excel">
        <WindowHeight>10500</WindowHeight>
        <WindowWidth>22188</WindowWidth>
        <WindowTopX>32767</WindowTopX>
        <WindowTopY>32767</WindowTopY>
        <ProtectStructure>False</ProtectStructure>
        <ProtectWindows>False</ProtectWindows>
    </ExcelWorkbook>
    <Styles>
        <Style ss:ID="Default" ss:Name="Normal">
            <Alignment ss:Vertical="Bottom"/>
            <Borders/>
            <Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000"/>
            <Interior/>
            <NumberFormat/>
            <Protection/>
        </Style>
        <Style ss:ID="m2095674168064">
            <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
            <Borders>
                <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
            </Borders>
            <Font ss:FontName="仿宋_GB2312" x:CharSet="134" ss:Size="14" ss:Color="#000000"/>
        </Style>
        <Style ss:ID="m2095674168084">
            <Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/>
            <Borders>
                <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
            </Borders>
            <Font ss:FontName="仿宋_GB2312" x:CharSet="134" ss:Size="14" ss:Color="#000000"/>
        </Style>
        <Style ss:ID="s64">
            <Alignment ss:Horizontal="Left" ss:Vertical="Center"/>
            <Font ss:FontName="仿宋_GB2312" x:CharSet="134" ss:Size="14" ss:Color="#000000"/>
        </Style>
        <Style ss:ID="s65">
            <Font ss:FontName="仿宋_GB2312" x:CharSet="134" ss:Size="14" ss:Color="#000000"/>
        </Style>
        <Style ss:ID="s71">
            <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
            <Borders>
                <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
            </Borders>
            <Font ss:FontName="仿宋_GB2312" x:CharSet="134" ss:Size="14" ss:Color="#000000"/>
        </Style>
        <Style ss:ID="s74">
            <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
            <Font ss:FontName="方正小标宋简体" x:CharSet="134" ss:Size="24" ss:Color="#000000"/>
        </Style>
        <Style ss:ID="s75">
            <Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/>
            <Font ss:FontName="仿宋_GB2312" x:CharSet="134" ss:Size="14" ss:Color="#000000"/>
        </Style>
        <Style ss:ID="s76">
            <Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/>
            <Borders>
                <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
            </Borders>
            <Font ss:FontName="仿宋_GB2312" x:CharSet="134" ss:Size="12" ss:Color="#000000"/>
        </Style>
    </Styles>
    <Worksheet ss:Name="Sheet1">
        <Table ss:ExpandedColumnCount="4" ss:ExpandedRowCount="8" x:FullColumns="1"
   x:FullRows="1" ss:DefaultColumnWidth="48.6" ss:DefaultRowHeight="14.4">
            <Column ss:AutoFitWidth="0" ss:Width="36.599999999999994"/>
            <Column ss:AutoFitWidth="0" ss:Width="224.4"/>
            <Column ss:AutoFitWidth="0" ss:Width="386.4"/>
            <Column ss:AutoFitWidth="0" ss:Width="147"/>
            <Row ss:AutoFitHeight="0" ss:Height="38.25">
                <Cell ss:MergeAcross="3" ss:StyleID="s74">
                    <Data ss:Type="String">校车运行方案</Data>
                </Cell>
            </Row>
            <Row ss:AutoFitHeight="0" ss:Height="23.25" ss:StyleID="s64">
                <Cell ss:MergeAcross="3" ss:StyleID="s75">
                    <ss:Data ss:Type="String"
                        xmlns="http://www.w3.org/TR/REC-html40"><Font html:Color="#000000">所属单位（盖章）：</Font><U><Font html:Color="#000000"> ${secondParty!} </Font></U><Font html:Color="#000000">     车牌号码：</Font><U><Font html:Color="#000000"> ${plateNumber!} </Font></U>
                    </ss:Data>
                </Cell>
            </Row>
            <Row ss:AutoFitHeight="0" ss:Height="45.9" ss:StyleID="s64">
                <Cell ss:MergeAcross="3" ss:StyleID="s75">
                    <ss:Data ss:Type="String"
                        xmlns="http://www.w3.org/TR/REC-html40"><Font html:Color="#000000">驾驶员（姓名/电话）：</Font><U><Font html:Color="#000000"> ${driver!}/${mobile!} </Font></U><Font html:Color="#000000">            随车接送人员（姓名/电话）：</Font><U><Font html:Color="#000000"> ${caretaker!}/${caretakeMobile!} </Font></U>
                    </ss:Data>
                </Cell>
            </Row>
            <Row ss:AutoFitHeight="0" ss:Height="51" ss:StyleID="s64">
                <Cell ss:MergeAcross="3" ss:StyleID="s75">
                    <ss:Data ss:Type="String"
                             xmlns="http://www.w3.org/TR/REC-html40"><Font html:Color="#000000">开行时间：</Font><U><#list signApplyNumList as signApplyNum><Font html:Color="#000000">第${signApplyNum_index+1}趟：${signApplyNum.startTime}至${signApplyNum.endTime}</Font><step>          </step></step></#list></U>
                    </ss:Data>
                </Cell>
            </Row>
            <Row ss:AutoFitHeight="0" ss:Height="23.25" ss:StyleID="s64">
                <Cell ss:MergeAcross="3" ss:StyleID="s76">
                    <ss:Data ss:Type="String"
                             xmlns="http://www.w3.org/TR/REC-html40"><Font html:Color="#000000">行驶途经社区：</Font><U>  ${passTowns!}  </U><Font html:Color="#000000">                              </Font></U>
                    </ss:Data>
                </Cell>
            </Row>
            <Row ss:AutoFitHeight="0" ss:Height="27.75" ss:StyleID="s65">
                <Cell ss:MergeAcross="1" ss:StyleID="m2095674168064">
                    <Data ss:Type="String">途经道路</Data>
                </Cell>
                <Cell ss:StyleID="s71">
                    <Data ss:Type="String">途经站点</Data>
                </Cell>
                <Cell ss:StyleID="s71">
                    <Data ss:Type="String">备注</Data>
                </Cell>
            </Row>
            <Row ss:AutoFitHeight="0" ss:Height="150" ss:StyleID="s65">
                <Cell ss:MergeAcross="1" ss:StyleID="m2095674168084">
                    <Data ss:Type="String">${passRoads!}</Data>
                </Cell>
                <Cell ss:StyleID="s71">
                    <Data ss:Type="String">${dockingStation!}</Data>
                </Cell>
                <Cell ss:StyleID="s71"/>
            </Row>
            <Row ss:AutoFitHeight="0" ss:Height="77.099999999999994" ss:StyleID="s65">
                <Cell ss:MergeAcross="3" ss:StyleID="s75">
                    <ss:Data ss:Type="String"
                        xmlns="http://www.w3.org/TR/REC-html40">
                        <Font html:Color="#FF0000">&#10;虎门教育办：                        虎门交警大队：                        虎门交通分局：             &#10;                                                                                         &#10;                                                                  </Font><Font html:Color="#000000">审查日期：      年       月       日          </Font>
                    </ss:Data>
                </Cell>
            </Row>
        </Table>
        <WorksheetOptions
            xmlns="urn:schemas-microsoft-com:office:excel">
            <PageSetup>
                <Layout x:Orientation="Landscape"/>
                <Header x:Margin="0.3"/>
                <Footer x:Margin="0.3"/>
                <PageMargins x:Bottom="0.75" x:Left="0.31388888888888899"
     x:Right="0.235416666666667" x:Top="0.59027777777777801"/>
            </PageSetup>
            <Print>
                <ValidPrinterInfo/>
                <PaperSizeIndex>9</PaperSizeIndex>
                <HorizontalResolution>600</HorizontalResolution>
                <VerticalResolution>600</VerticalResolution>
            </Print>
            <Selected/>
            <Panes>
                <Pane>
                    <Number>3</Number>
                    <ActiveRow>7</ActiveRow>
                    <ActiveCol>9</ActiveCol>
                </Pane>
            </Panes>
            <ProtectObjects>False</ProtectObjects>
            <ProtectScenarios>False</ProtectScenarios>
        </WorksheetOptions>
    </Worksheet>
</Workbook>
