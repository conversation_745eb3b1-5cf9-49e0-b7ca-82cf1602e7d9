<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.oa.dal.mysql.ferrydutyregister.FerryDutyRegisterMapper">

    <resultMap id="ferryDutyRegisterExtendResult" type="cn.iocoder.yudao.module.oa.controller.admin.ferrydutyregister.vo.FerryDutyRegisterPageReqVO">
        <result column="id" property="id" />
        <result column="apply_id" property="applyId" />
        <result column="apply_name" property="applyName" />
        <result column="apply_phone_number" property="applyPhoneNumber" />
        <result column="apply_dept_name" property="applyDeptName" />
        <result column="apply_dept_id" property="applyDeptId" />
        <result column="partner_dept_id" property="partnerDeptId" />
        <result column="partner_id" property="partnerId" />
        <result column="partner_name" property="partnerName" />
        <result column="partner_dept_name" property="partnerDeptName" />
        <result column="partner_phone_number" property="partnerPhoneNumber" />
        <result column="begin_time" property="beginTime" />
        <result column="end_time" property="endTime" />
    </resultMap>

    <select id="selectFerryDutyRegisterPage" resultMap="ferryDutyRegisterExtendResult">
        SELECT
        fdr.*,
        emp_apply.name AS apply_name,
        emp_apply.phone_number AS apply_phone_number,
        dept_apply.name AS apply_dept_name,
        dept_apply.id AS apply_dept_id,
        emp_partner.name AS partner_name,
        emp_partner.phone_number AS partner_phone_number,
        dept_partner.name AS partner_dept_name,
        dept_partner.id AS partner_dept_id
        FROM t_ferry_duty_register fdr
        LEFT JOIN t_monthly_salary_employees emp_apply
        ON fdr.apply_id = emp_apply.id AND emp_apply.deleted = 0
        LEFT JOIN system_dept dept_apply
        ON emp_apply.dept_id = dept_apply.id AND dept_apply.deleted = 0
        LEFT JOIN t_monthly_salary_employees emp_partner
        ON fdr.partner_id = emp_partner.id AND emp_partner.deleted = 0
        LEFT JOIN system_dept dept_partner
        ON emp_partner.dept_id = dept_partner.id AND dept_partner.deleted = 0
        <where>
            fdr.deleted = 0
            <if test="query.applyName != null and query.applyName != ''">
                AND (emp_apply.name LIKE CONCAT('%', #{query.applyName}, '%')
                OR emp_partner.name LIKE CONCAT('%', #{query.applyName}, '%'))
            </if>
            <if test="query.applyDeptId != null and query.applyDeptId !=''">
                AND (emp_apply.dept_id = #{query.applyDeptId}
                OR emp_partner.dept_id = #{query.applyDeptId})
            </if>
            <if test="query.beginTime != null">
                AND fdr.begin_time &gt;= #{query.beginTime}
            </if>
            <if test="query.endTime != null">
                AND fdr.end_time &lt;= #{query.endTime}
            </if>
            <!-- 其他查询条件可继续追加 -->
        </where>
        ORDER BY fdr.begin_time DESC
    </select>

    <!--根据 ID 查询详情-->
    <select id="selectFerryDutyRegisterDetail" resultMap="ferryDutyRegisterExtendResult">
        SELECT
        fdr.*,
        emp_apply.name AS apply_name,
        emp_apply.phone_number AS apply_phone_number,
        dept_apply.name AS apply_dept_name,
        dept_apply.id AS apply_dept_id,
        emp_partner.name AS partner_name,
        emp_partner.phone_number AS partner_phone_number,
        dept_partner.name AS partner_dept_name,
        dept_partner.id AS partner_dept_id
        FROM t_ferry_duty_register fdr
        LEFT JOIN t_monthly_salary_employees emp_apply
        ON fdr.apply_id = emp_apply.id AND emp_apply.deleted = 0
        LEFT JOIN system_dept dept_apply
        ON emp_apply.dept_id = dept_apply.id AND
        dept_apply.deleted = 0
        LEFT JOIN t_monthly_salary_employees emp_partner
        ON fdr.partner_id = emp_partner.id AND emp_partner.deleted = 0
        LEFT JOIN system_dept dept_partner
        ON emp_partner.dept_id = dept_partner.id AND
        dept_partner.deleted = 0
        <where>
            fdr.id = #{id}
        </where>
        AND fdr.deleted = 0
    </select>
</mapper>