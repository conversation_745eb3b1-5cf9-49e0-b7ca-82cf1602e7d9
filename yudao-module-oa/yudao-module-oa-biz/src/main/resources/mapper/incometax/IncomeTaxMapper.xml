<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.oa.dal.mysql.incometax.IncomeTaxMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectIncomeTaxPage"
            resultType="cn.iocoder.yudao.module.oa.controller.admin.incometax.vo.IncomeTaxRespVO">
        SELECT
        d.id,
        d.company_name,
        d.person_id,
        d.identity,
        d.identity_type,
        d.tax_amount,
        d.remark,
        d.occurrence_time,
        d.creator,
        d.create_time,
        d.updater,
        d.update_time,
        e.name as person_name,
        f.name as dept_name,
        f.id,
        g.id,
        g.name as post_name
        FROM t_income_tax d
        LEFT JOIN t_monthly_salary_employees e ON d.person_id = e.id and e.deleted = 0
        LEFT JOIN system_dept f ON e.dept_id = f.id and f.deleted = 0
        LEFT JOIN system_post g ON e.post_id = g.id and g.deleted = 0
        <where>
            <if test="query.identity != null">
                AND d.identity LIKE CONCAT('%', #{query.identity}, '%')
            </if>
            <if test="query.personId != null and query.personId != ''">
                AND d.person_id = #{query.personId}
            </if>
            <if test="query.personName != null">
                AND e.name LIKE CONCAT('%', #{query.personName}, '%')
            </if>
            <if test="query.createTime != null and query.createTime.length >= 1">
                <if test="query.createTime[0] != null">
                    AND d.create_time &gt;= #{query.createTime[0]}
                </if>
                <if test="query.createTime.length >= 2 and query.createTime[1] != null">
                    AND d.create_time &lt;= #{query.createTime[1]}
                </if>
            </if>
            AND d.deleted = 0
        </where>
        ORDER BY d.create_time DESC
    </select>
</mapper>
