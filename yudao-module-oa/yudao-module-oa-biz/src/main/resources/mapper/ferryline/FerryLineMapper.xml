<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.oa.dal.mysql.ferryline.FerryLineMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->



    <select id="getFerryLineBySchoolandlineName"
            resultType="cn.iocoder.yudao.module.oa.controller.admin.ferryline.vo.FerryLineRespVO">
        select * from t_ferry_line where school = #{school} and line_name = #{lineName} and deleted = 0 limit 1
    </select>

    <select id="getLineBySchool" resultType="cn.iocoder.yudao.module.oa.dal.dataobject.ferryline.FerryLineDO">
        SELECT *
        FROM t_ferry_line
        WHERE school =#{school} and deleted = 0
    </select>
    <select id="getAllSchool" resultType="java.lang.String">
        select  school from t_ferry_line
         WHERE deleted = 0
    </select>
    <select id="getFerryLineListBySchoolandlineName"
            resultType="cn.iocoder.yudao.module.oa.controller.admin.ferryline.vo.FerryLineRespVO">
        select * from t_ferry_line where school = #{school} and line_name = #{lineName} and deleted = 0
    </select>
    <select id="getFerryLineBySchoolandlineNameList" resultType="cn.iocoder.yudao.module.oa.controller.admin.ferryline.vo.FerryLineRespVO">
        select * from t_ferry_line where school = #{school} and line_name = #{lineName} and deleted = 0
    </select>
</mapper>
