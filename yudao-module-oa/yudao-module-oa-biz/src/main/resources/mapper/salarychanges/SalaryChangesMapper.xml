<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.oa.dal.mysql.salarychanges.SalaryChangesMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <resultMap id="SalaryChangesResult" type="cn.iocoder.yudao.module.oa.dal.dataobject.salarychanges.SalaryChangesDO">
            <id property="id" column="id" />
            <result property="personId" column="person_id" />
            <result property="changeCategory" column="change_category" />
            <result property="infoBeforeChange" column="info_before_change" />
            <result property="infoAfterChange" column="info_after_change" />
            <result property="changeTime" column="change_time" />
            <result property="changeUserId" column="change_user_id" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="deleted" column="deleted" />
    </resultMap>

    <resultMap id="SalaryChangesExtendResult" type="cn.iocoder.yudao.module.oa.controller.admin.salarychanges.vo.SalaryChangesPageReqVO">
            <id property="id" column="id" />
            <result property="personId" column="person_id" />
            <result property="changeCategory" column="change_category" />
            <result property="infoBeforeChange" column="info_before_change" />
            <result property="infoAfterChange" column="info_after_change" />
             <result property="changeTime" column="change_time" />
            <result property="changeUserId" column="change_user_id" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="name" column="name" />
            <result property="companyName" column="company_name" />
            <result property="postName" column="post_name" />
            <result property="deptName" column="dept_name"/>
            <result property="idCardNumber" column="id_card_number" />
            <result property="changeUserName" column="change_user_name"/>
    </resultMap>
    <select id="selectSalaryChangesList" resultMap="SalaryChangesExtendResult">
        SELECT
            s.id,
            s.person_id,
            s.change_category,
            s.info_before_change,
            s.info_after_change,
            s.change_time,
            s.change_user_id,
            s.create_time,
            s.update_time,
            e.name,
            e.company_name,
            f.name as post_name,
            g.name as dept_name,
            e.id_card_number,
            u.nickname AS change_user_name
        FROM t_salary_changes s
        LEFT JOIN t_monthly_salary_employees e ON s.person_id = e.id and e.deleted = 0
        LEFT JOIN system_dept g ON e.dept_id = g.id and g.deleted = 0
        LEFT JOIN system_post f ON e.post_id = f.id and f.deleted = 0
        LEFT JOIN system_users u ON s.change_user_id = u.id and u.deleted = 0
        <where>
            <if test="query.name != null and query.name != ''">
                AND e.name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.personId != null and query.personId != ''">
                AND s.person_id LIKE CONCAT('%', #{query.personId}, '%')
            </if>
            <if test="query.companyName != null and query.companyName != ''">
                AND e.company_name LIKE CONCAT('%', #{query.companyName}, '%')
            </if>
            <if test="query.postName != null and query.postName !=''">
                AND e.post_name LIKE CONCAT('%', #{query.postName}, '%')
            </if>
            <if test="query.deptName != null and query.deptName != ''">
                AND g.name LIKE CONCAT('%', #{query.deptName}, '%')
            </if>
            <if test="query.changeTimeStart!=null">
                AND s.change_time &gt; #{query.changeTimeStart}
            </if>
            <if test="query.changeTimeEnd!=null">
                AND s.change_time &lt; #{query.changeTimeEnd}
            </if>
                    <!-- 动态生成 BETWEEN 条件 -->
               AND s.deleted = 0
        </where>
        order by s.change_time DESC
    </select>
    <!--根据 ID 查询详情-->
    <select id="selectSalaryChangesDetail" resultMap="SalaryChangesExtendResult">
        SELECT
            s.id,
            s.person_id,
            s.change_category,
            s.info_before_change,
            s.info_after_change,
            s.change_time,
            s.change_user_id,
            s.create_time,
            s.update_time,
            e.name,
            e.company_name,
            f.name as post_name,
            g.name as dept_name,
            e.id_card_number,
            u.nickname AS change_user_name
        FROM t_salary_changes s
        LEFT JOIN t_monthly_salary_employees e ON s.person_id = e.id and e.deleted = 0
        LEFT JOIN system_dept g ON e.dept_id = g.id and g.deleted = 0
        LEFT JOIN system_post f ON e.post_id = f.id and f.deleted = 0
        LEFT JOIN system_users u ON s.change_user_id = u.id and u.deleted = 0
        <where>
            <if test="id != null">
                AND s.id = #{id}
            </if>
        </where>
    </select>
</mapper>
