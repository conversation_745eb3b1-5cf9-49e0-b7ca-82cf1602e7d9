<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.oa.dal.mysql.monthlysalaryemployees.MonthlySalaryEmployeesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="MonthlySalaryEmployeesResult" type="cn.iocoder.yudao.module.oa.dal.dataobject.monthlysalaryemployees.MonthlySalaryEmployeesDO">
        <id property="id" column="id"/>
        <result property="companyName" column="company_name"/>
        <result property="archiveNumber" column="archive_number"/>
        <result property="deptId" column="dept_id"/>
        <result property="postId" column="post_id"/>
        <result property="district" column="district"/>
        <result property="townStreet" column="town_street"/>
        <result property="assignedLocation" column="assigned_location"/>
        <result property="startDate" column="start_date"/>
        <result property="name" column="name"/>
        <result property="licensePlate" column="license_plate"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="sex" column="sex"/>
        <result property="yearsOfService" column="years_of_service"/>
        <result property="dateOfBirth" column="date_of_birth"/>
        <result property="driverLicenseArchiveNumber" column="driver_license_archive_number"/>
        <result property="driverLicenseExpiryDateStart" column="driver_license_expiry_date_start"/>
        <result property="driverLicenseExpiryDateEnd" column="driver_license_expiry_date_end"/>
        <result property="initialLicenseIssueDate" column="initial_license_issue_date"/>
        <result property="driverLicenseRenewalDateStart" column="driver_license_renewal_date_start"/>
        <result property="driverLicenseRenewalDateEnd" column="driver_license_renewal_date_end"/>
        <result property="drivingLicenses" column="driving_licenses"/>
        <result property="schoolBusQualificationDate" column="school_bus_qualification_date"/>
        <result property="idCardNumber" column="id_card_number"/>
        <result property="idCardExpiryDateStart" column="id_card_expiry_date_start"/>
        <result property="idCardExpiryDateEnd" column="id_card_expiry_date_end"/>
        <result property="householdRegistration" column="household_registration"/>
        <result property="householdAddress" column="household_address"/>
        <result property="residenceAddress" column="residence_address"/>
        <result property="emergencyContactNumber" column="emergency_contact_number"/>
        <result property="emergencyContactNumber2" column="emergency_contact_number2"/>
        <result property="emergencyContactName" column="emergency_contact_name"/>
        <result property="emergencyContactName2" column="emergency_contact_name2"/>
        <result property="emergencyContactRelationship" column="emergency_contact_relationship"/>
        <result property="emergencyContactRelationship2" column="emergency_contact_relationship2"/>
        <result property="bankBranch" column="bank_branch"/>
        <result property="bankAccountNumber" column="bank_account_number"/>
        <result property="socialInsuranceApplication" column="social_insurance_application"/>
        <result property="housingFundApplication" column="housing_fund_application"/>
        <result property="remark1" column="remark1"/>
        <result property="contractYears" column="contract_years"/>
        <result property="contractStartDate" column="contract_start_date"/>
        <result property="contractEndDate" column="contract_end_date"/>
        <result property="remark2" column="remark2"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="updater" column="updater"/>
        <result property="deleted" column="deleted"/>
    </resultMap>


        <!-- 通用查询映射结果 -->
    <resultMap id="MonthlySalaryEmployeesResultExtend" type="cn.iocoder.yudao.module.oa.controller.admin.monthlysalaryemployees.vo.MonthlySalaryEmployeesPageReqVO">
        <result property="companyName" column="company_name"/>
        <result property="archiveNumber" column="archive_number"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="postId" column="post_id"/>
        <result property="postName" column="post_name"/>
        <result property="district" column="district"/>
        <result property="townStreet" column="town_street"/>
        <result property="assignedLocation" column="assigned_location"/>
        <result property="startDate" column="start_date"/>
        <result property="name" column="name"/>
        <result property="licensePlate" column="license_plate"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="sex" column="sex"/>
        <result property="yearsOfService" column="years_of_service"/>
        <result property="dateOfBirth" column="date_of_birth"/>
        <result property="driverLicenseArchiveNumber" column="driver_license_archive_number"/>
        <result property="driverLicenseExpiryDateStart" column="driver_license_expiry_date_start"/>
        <result property="driverLicenseExpiryDateEnd" column="driver_license_expiry_date_end"/>
        <result property="initialLicenseIssueDate" column="initial_license_issue_date"/>
        <result property="driverLicenseRenewalDateStart" column="driver_license_renewal_date_start"/>
        <result property="driverLicenseRenewalDateEnd" column="driver_license_renewal_date_end"/>
        <result property="drivingLicenses" column="driving_licenses"/>
        <result property="schoolBusQualificationDate" column="school_bus_qualification_date"/>
        <result property="idCardNumber" column="id_card_number"/>
        <result property="idCardExpiryDateStart" column="id_card_expiry_date_start"/>
        <result property="idCardExpiryDateEnd" column="id_card_expiry_date_end"/>
        <result property="householdRegistration" column="household_registration"/>
        <result property="householdAddress" column="household_address"/>
        <result property="residenceAddress" column="residence_address"/>
        <result property="emergencyContactNumber" column="emergency_contact_number"/>
        <result property="emergencyContactNumber2" column="emergency_contact_number2"/>
        <result property="emergencyContactName" column="emergency_contact_name"/>
        <result property="emergencyContactName2" column="emergency_contact_name2"/>
        <result property="emergencyContactRelationship" column="emergency_contact_relationship"/>
        <result property="emergencyContactRelationship2" column="emergency_contact_relationship2"/>
        <result property="bankBranch" column="bank_branch"/>
        <result property="bankAccountNumber" column="bank_account_number"/>
        <result property="socialInsuranceApplication" column="social_insurance_application"/>
        <result property="housingFundApplication" column="housing_fund_application"/>
        <result property="remark1" column="remark1"/>
        <result property="contractYears" column="contract_years"/>
        <result property="contractStartDate" column="contract_start_date"/>
        <result property="contractEndDate" column="contract_end_date"/>
        <result property="remark2" column="remark2"/>
        <result property="status" column="status"/>
    </resultMap>

    <select id="selectCustomPage" resultMap="MonthlySalaryEmployeesResultExtend">
        SELECT a.*,
            b.name as dept_name,
            d.name as post_name
        from t_monthly_salary_employees a left join system_dept b on b.id = a.dept_id and b.deleted = 0
        left join system_post d on d.id = a.post_id and d.deleted = 0
        <where>
            a.deleted = 0
            <if test="query.id != null">
                AND a.id = #{query.id}
            </if>
            <if test="query.name != null and query.name != ''">
                AND a.name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.phoneNumber != null and query.phoneNumber != ''">
                    AND a.phone_number LIKE CONCAT('%', #{query.phoneNumber}, '%')
            </if>
            <if test="query.idCardNumber != null and query.idCardNumber != ''">
                AND a.id_card_number LIKE CONCAT('%', #{query.idCardNumber}, '%')
            </if>
            <!--部门名称模糊查询-->
            <if test="query.deptName != null and query.deptName != ''">
                AND b.name LIKE CONCAT('%', #{query.deptName}, '%')
            </if>
            <if test="query.postName != null and query.postName != ''">
                AND d.name LIKE CONCAT('%', #{query.postName}, '%')
            </if>
        </where>
        order by a.id desc
    </select>

    <!-- 根据ID查询月工资人员花名册 -->
    <select id="selectCustomByPersonId" parameterType="Long" resultMap="MonthlySalaryEmployeesResultExtend">
        SELECT a.*,
            e.name as dept_name,
            d.name as post_name
        FROM t_monthly_salary_employees a left join system_dept e on e.id = a.dept_id and e.deleted = 0
        left join system_post d on d.id = a.post_id and d.deleted = 0
        <where>
            a.id = #{id}
        </where>
    </select>
    <select id="selectPersonLike"
            resultType="cn.iocoder.yudao.module.oa.dal.dataobject.monthlysalaryemployees.MonthlySalaryEmployeesDO">
        SELECT * FROM t_monthly_salary_employees WHERE name LIKE CONCAT('%', #{name}, '%')
    </select>

    <!--查询参加了某周的周摆渡但该周摆渡结算未确认的司机-->
    <select id="selectDriverNotWeekConfirm" parameterType="String" resultMap="MonthlySalaryEmployeesResultExtend">
        select tse.name,tse.phone_number,tse.id_card_number from t_monthly_salary_employees tse
        left join system_post sp on tse.post_id = sp.id and sp.deleted = 0
                                                           left join t_week_salary ts on tse.phone_number = ts.mobile and ts.deleted = false
                                                           left join t_week_salary_confirm twc on ts.week = twc.week and twc.deleted = false
            <where>
                tse.deleted = false and tse.status =1 and twc.status = '0' and twc.week like CONCAT('%', #{week}, '%') and sp.code='normaldriver' and twc.deleted = false;
             </where>

    </select>

    <select id="getEmployeesPage" resultType="cn.iocoder.yudao.module.oa.controller.admin.monthlysalaryemployees.vo.EmployeesRespVO">
        select a.*,p.name post_name from (SELECT name,
                              id_card_number,
                              phone_number,
                              sex,
                              date_of_birth,
                              driving_licenses,
                              school_bus_qualification_date,
                              driver_license_renewal_date_end,
                              driver_license_expiry_date_end,
                              post_id
                       FROM t_monthly_salary_employees where deleted = 0
                       union all
                       SELECT name, identity, mobile, sex, birthdate, drive_model, bus_drive_date, null,driver_license_end, post_id
                       FROM t_person_info where deleted = 0) a left join system_post p on a.post_id = p.id
        <where>
            <if test="query.name != null and query.name != ''">
                AND a.name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.phoneNumber != null and query.phoneNumber != ''">
                AND a.phone_number LIKE CONCAT('%', #{query.phoneNumber}, '%')
            </if>
            <if test="query.idCardNumber != null and query.idCardNumber != ''">
                AND a.id_card_number LIKE CONCAT('%', #{query.idCardNumber}, '%')
            </if>
            <if test="query.postIds != null and query.postIds.length > 0">
                AND a.post_id in
                <foreach collection="query.postIds" item="postId" index="index" open="(" close=")" separator=",">
                    #{postId}
                </foreach>
            </if>
            <if test="query.postName != null and query.postName != ''">
                AND p.name LIKE CONCAT('%', #{query.postName}, '%')
            </if>
        </where>
    </select>
    <select id="selectAssignedLocationLike" resultType="java.lang.String">
        select assigned_location from t_monthly_salary_employees where assigned_location like CONCAT('%', #{assignedLocation}, '%')
    </select>
    <select id="selectCompanyLike" resultType="java.lang.String">
        select company_name from t_monthly_salary_employees where company_name like CONCAT('%', #{companyName}, '%')
    </select>

</mapper>
