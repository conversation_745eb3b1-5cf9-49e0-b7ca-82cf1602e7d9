<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.oa.dal.mysql.overtimesubsidy.OvertimeSubsidyMapper">
    <update id="updateByPersonId">
        UPDATE t_overtime_subsidy
        SET
            district = #{district},
            phone_number = #{phoneNumber},
            dept_name = #{deptName},
            overtime_hours = #{overtimeHours},
            price = #{price},
            overtime_pay = #{overtimePay},
            overtime_count = #{overtimeCount},
            subsidy = #{subsidy},
            occurrence_time = #{occurrenceTime},
            remark = #{remark}
        WHERE
            person_id = #{personId}
    </update>

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectOvertimeSubsidyPage"
            resultType="cn.iocoder.yudao.module.oa.controller.admin.overtimesubsidy.vo.OvertimeSubsidyRespVO">
        SELECT
        d.id,
        d.district,
        d.person_id,
        d.phone_number,
        d.dept_name,
        d.overtime_hours,
        d.price,
        d.overtime_pay,
        d.overtime_count,
        d.subsidy,
        d.occurrence_time,
        d.remark,
        d.create_time,
        e.name as person_name,
        e.company_name,
        e.district,
        e.phone_number,
        e.id_card_number,
        e.bank_account_number,
        e.license_plate,
        e.social_insurance_application,
        e.housing_fund_application,
        e.start_date,
        f.id,
        g.id,
        g.name as post_name
        FROM t_overtime_subsidy d
        LEFT JOIN t_monthly_salary_employees e ON d.person_id = e.id and e.deleted = 0
        LEFT JOIN system_dept f ON e.dept_id = f.id and f.deleted = 0
        LEFT JOIN system_post g ON e.post_id = g.id and g.deleted = 0
        <where>
            <if test="query.personName != null">
                AND e.name LIKE CONCAT('%', #{query.personName}, '%')
            </if>
            <if test="query.personId != null and query.personId != ''">
                AND d.person_id = #{query.personId}
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND e.dept_id = #{query.deptId}
            </if>
            <if test="query.deptName != null and query.deptName != ''">
                AND d.dept_name LIKE CONCAT('%', #{query.deptName}, '%')
            </if>
            <if test="query.occurrenceTime != null and query.occurrenceTime != ''">
                AND d.occurrence_time LIKE CONCAT('%', #{query.occurrenceTime}, '%')
            </if>
            <if test="query.createTime != null and query.createTime.length >= 1">
                <if test="query.createTime[0] != null">
                    AND d.create_time &gt;= #{query.createTime[0]}
                </if>
                <if test="query.createTime.length >= 2 and query.createTime[1] != null">
                    AND d.create_time &lt;= #{query.createTime[1]}
                </if>
            </if>
            AND d.deleted = 0
            AND g.code != 'ferry'
            AND g.code != 'normaldriver'
            AND g.code != 'follow'
            AND g.code != 'normalfollowcar'
        </where>
        ORDER BY d.create_time DESC
    </select>
    <select id="selectOvertimeSubsidyDriverPage"
            resultType="cn.iocoder.yudao.module.oa.controller.admin.overtimesubsidy.vo.OvertimeSubsidyRespVO">
        SELECT
        d.id,
        d.district,
        d.person_id,
        d.phone_number,
        d.dept_name,
        d.overtime_hours,
        d.price,
        d.overtime_pay,
        d.overtime_count,
        d.subsidy,
        d.occurrence_time,
        d.remark,
        d.create_time,
        e.name as person_name,
        e.company_name,
        e.district,
        e.phone_number,
        e.id_card_number,
        e.bank_account_number,
        e.license_plate,
        e.social_insurance_application,
        e.housing_fund_application,
        e.start_date,
        f.id,
        g.id,
        g.name as post_name
        FROM t_overtime_subsidy d
        LEFT JOIN t_monthly_salary_employees e ON d.person_id = e.id and e.deleted = 0
        LEFT JOIN system_dept f ON e.dept_id = f.id and f.deleted = 0
        LEFT JOIN system_post g ON e.post_id = g.id and g.deleted = 0
        <where>
            <if test="query.personName != null">
                AND e.name LIKE CONCAT('%', #{query.personName}, '%')
            </if>
            <if test="query.personId != null and query.personId != ''">
                AND d.person_id = #{query.personId}
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND e.dept_id = #{query.deptId}
            </if>
            <if test="query.deptName != null and query.deptName != ''">
                AND d.dept_name LIKE CONCAT('%', #{query.deptName}, '%')
            </if>
            <if test="query.occurrenceTime != null and query.occurrenceTime != ''">
                AND d.occurrence_time LIKE CONCAT('%', #{query.occurrenceTime}, '%')
            </if>
            <if test="query.createTime != null and query.createTime.length >= 1">
                <if test="query.createTime[0] != null">
                    AND d.create_time &gt;= #{query.createTime[0]}
                </if>
                <if test="query.createTime.length >= 2 and query.createTime[1] != null">
                    AND d.create_time &lt;= #{query.createTime[1]}
                </if>
            </if>
            AND d.deleted = 0
            AND (g.code = 'ferry' OR g.code = 'normaldriver' OR g.code = 'follow' OR g.code = 'normalfollowcar')
        </where>
        ORDER BY d.create_time DESC
    </select>
    <select id="selectByPhoneNumberThisMonth"
            resultType="cn.iocoder.yudao.module.oa.dal.dataobject.overtimesubsidy.OvertimeSubsidyDO">
        SELECT
        d.id,
        d.district,
        d.person_id,
        d.phone_number,
        d.dept_name,
        d.overtime_hours,
        d.price,
        d.overtime_pay,
        d.overtime_count,
        d.subsidy,
        d.occurrence_time,
        d.remark,
        d.creator,
        d.create_time,
        d.updater,
        d.update_time,
        d.deleted
            from t_overtime_subsidy d
            where d.phone_number = #{phoneNumber}
            AND d.occurrence_time = #{occurrenceTime}
            AND d.deleted = 0

    </select>
</mapper>
