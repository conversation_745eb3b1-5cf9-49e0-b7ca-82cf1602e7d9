<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.oa.dal.mysql.officestaffsalary.OfficeStaffSalaryMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <resultMap id="OfficeStaffSalaryResult" type="cn.iocoder.yudao.module.oa.controller.admin.officestaffsalary.vo.OfficeStaffSalaryPageReqVO">
        <id property="id" column="id"/>
        <result property="personId" column="person_id"/>
        <result property="name" column="name" />
        <result property="idCardNumber" column="id_card_number"/>
        <result property="postName" column="post_name"/>
        <result property="deptName" column="dept_name"/>
        <result property="salaryMethod" column="salary_method"/>
        <result property="monthlyGrossSalary" column="monthly_gross_salary"/>
        <result property="paidMonths" column="paid_months"/>
        <result property="monthPerformance" column="month_performance"/>
        <result property="postWage" column="post_wage"/>
        <result property="postSubsidy" column="post_subsidy"/>
        <result property="safetyTrainingSubsidy" column="safety_training_subsidy"/>
        <result property="basicSalary" column="basic_salary"/>
        <result property="attendanceAward" column="attendance_award"/>
        <result property="seniorityAward" column="seniority_award"/>
        <result property="taskSalary" column="task_salary"/>
        <result property="fixedOvertimeFee" column="fixed_overtime_fee"/>
        <result property="mealsFee" column="meals_fee"/>
        <result property="housingFee" column="housing_fee"/>
        <result property="travelFee" column="travel_fee"/>
        <result property="phoneFee" column="phone_fee"/>
        <result property="otherFee" column="other_fee"/>
        <result property="salaryStatement" column="salary_statement"/>
        <result property="oilFee" column="oil_fee"/>
        <result property="holidayBaseSalary" column="holiday_base_salary"/>
        <result property="regularDate" column="regular_date"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="employeeStatus" column="employee_status" />
    </resultMap>

    <resultMap id="OfficeStaffSalaryDetailResult" type="cn.iocoder.yudao.module.oa.controller.admin.officestaffsalary.vo.OfficeStaffSalaryRespVO">
        <id property="id" column="id"/>
        <result property="personId" column="person_id"/>
        <result property="name" column="name" />
        <result property="idCardNumber" column="id_card_number"/>
        <result property="postName" column="post_name"/>
        <result property="deptName" column="dept_name"/>
        <result property="salaryMethod" column="salary_method"/>
        <result property="monthlyGrossSalary" column="monthly_gross_salary"/>
        <result property="paidMonths" column="paid_months"/>
        <result property="monthPerformance" column="month_performance"/>
        <result property="postWage" column="post_wage"/>
        <result property="postSubsidy" column="post_subsidy"/>
        <result property="safetyTrainingSubsidy" column="safety_training_subsidy"/>
        <result property="basicSalary" column="basic_salary"/>
        <result property="attendanceAward" column="attendance_award"/>
        <result property="seniorityAward" column="seniority_award"/>
        <result property="taskSalary" column="task_salary"/>
        <result property="fixedOvertimeFee" column="fixed_overtime_fee"/>
        <result property="mealsFee" column="meals_fee"/>
        <result property="housingFee" column="housing_fee"/>
        <result property="travelFee" column="travel_fee"/>
        <result property="phoneFee" column="phone_fee"/>
        <result property="otherFee" column="other_fee"/>
        <result property="salaryStatement" column="salary_statement"/>
        <result property="oilFee" column="oil_fee"/>
        <result property="holidayBaseSalary" column="holiday_base_salary"/>
        <result property="regularDate" column="regular_date"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
       <result property="employeeStatus" column="employee_status" />
    </resultMap>


    <!--手动实现分页查询-->
    <select id="selectOfficeStaffSalaryCustomerPage" resultMap="OfficeStaffSalaryResult">
        Select b.*,
        e.district,
        e.company_name,
        e.name,
        e.status as employee_status,
        e.id_card_number,
        e.phone_number,
        e.bank_account_number,
        e.start_date as hire_date,
        f.name as post_name,
        g.name as dept_name
        from t_office_staff_salary b
        left join t_monthly_salary_employees e
        on b.person_id = e.id and e.deleted = 0
        left join system_post f on e.post_id = f.id and f.deleted = 0
        left join system_dept g on e.dept_id = g.id and g.deleted = 0
        <where>
            b.deleted = 0
            <if test="query.id != null">
                AND b.id = #{query.id}
            </if>
            <if test="query.personId != null">
                AND b.person_id = #{query.personId}
            </if>
            <if test="query.name != null">
                AND e.name = #{query.name}
            </if>
            <if test="query.idCardNumber != null and query.idCardNumber != ''">
                AND e.id_card_number LIKE CONCAT('%', #{query.idCardNumber}, '%')
            </if>
            <if test="query.postName != null and query.postName != ''">
                AND f.name LIKE CONCAT('%', #{query.postName}, '%')
            </if>
            <if test="query.deptName != null and query.deptName != ''">
                AND g.name LIKE CONCAT('%', #{query.deptName}, '%')
            </if>
            <if test="query.companyName != null and query.companyName != ''">
                AND e.company_name LIKE CONCAT('%', #{query.companyName}, '%')
            </if>
        </where>
        order by b.id desc
    </select>

    <!--查询单个-->
    <select id="selectOfficeStaffSalaryDetail" resultMap="OfficeStaffSalaryDetailResult">
        Select b.*,
        e.district,
        e.company_name,
        e.name,
        e.status as employee_status,
        e.id_card_number,
        e.phone_number,
        e.bank_account_number,
        e.start_date as hire_date,
        f.name as post_name,
        g.name as dept_name
        from t_office_staff_salary b
        left join t_monthly_salary_employees e
        on b.person_id = e.id and e.deleted = 0
        left join system_post f on e.post_id = f.id and f.deleted = 0
        left join system_dept g on e.dept_id = g.id and g.deleted = 0
        <where>
            b.deleted = 0
            <if test="id != null">
                AND b.id = #{id}
            </if>
        </where>
    </select>

</mapper>
