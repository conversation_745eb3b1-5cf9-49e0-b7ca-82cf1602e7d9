<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.oa.dal.mysql.monthlyattendance.MonthlyAttendanceMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <resultMap id="MonthlyAttendanceResult" type="cn.iocoder.yudao.module.oa.dal.dataobject.monthlyattendance.MonthlyAttendanceDO">
        <id property="id" column="id" />
        <result property="personId" column="person_id"  />
        <result property="occurrenceTime"   column="occurrence_time"    />
        <result property="attendanceTotalDay"   column="attendance_total_day"   />
        <result property="attendanceDay"    column="attendance_day" />
        <result property="compensatoryDay"  column="compensatory_day"   />
        <result property="fakeDay"  column="fake_day"   />
        <result property="maternityDay" column="maternity_day"  />
        <result property="marriageDay"  column="marriage_day"   />
        <result property="vacation" column="vacation"   />
        <result property="absenceDay"   column="absence_day"    />
        <result property="personalDay"  column="personal_day"   />
        <result property="sickDay"  column="sick_day"   />
        <result property="absentee" column="absentee"   />
        <result property="bereavementDay"   column="bereavement_day"    />
        <result property="rest" column="rest"   />
        <result property="remark"   column="remark" />
        <result property="createTime"   column="create_time"    />
        <collection  property="monthlySalaryEmployeesDO"   javaType="MonthlySalaryEmployeesDO"  resultMap="monthlySalaryEmployees" />
    </resultMap>

    <resultMap id="monthlySalaryEmployees" type="cn.iocoder.yudao.module.oa.dal.dataobject.monthlysalaryemployees.MonthlySalaryEmployeesDO">
        <result property="companyName" column="company_name"    />
        <result property="deptId"   column="dept_id"    />
        <result property="name" column="name"   />
        <result property="phoneNumber"  column="phone_number"   />
    </resultMap>

    <select id="selectMonthlyAttendanceList" resultMap="MonthlyAttendanceResult">
        select tma.*, tmse.company_name, tmse.dept_id, tmse.name, tmse.phone_number from t_monthly_attendance tma
        left join t_monthly_salary_employees tmse on tma.person_id = tmse.id
        <where>
            <if test="query.companyName != null and query.companyName != ''">and tmse.company_name like concat('%', #{query.companyName}, '%')</if>
            <if test="query.deptId != null">and tmse.dept_id = #{query.deptId}</if>
            <if test="query.name != null and query.name != ''"> and tmse.name like concat('%', #{query.name}, '%')</if>
            <if test="query.createTime != null and query.createTime != ''">and tma.create_time between #{query.createTime[0]} and #{query.createTime[1]}</if>
            <if test="query.occurrenceTime != null and query.occurrenceTime != ''">and tma.occurrence_time = #{query.occurrenceTime}</if>
        </where>
    </select>
</mapper>