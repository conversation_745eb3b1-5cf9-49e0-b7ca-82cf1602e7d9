-- 将该建表 SQL 语句，添加到 yudao-module-infra-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "infra_student" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "name" varchar NOT NULL,
    "description" varchar NOT NULL,
    "birthday" varchar NOT NULL,
    "sex" int NOT NULL,
    "enabled" bit NOT NULL,
    "avatar" varchar NOT NULL,
    "video" varchar NOT NULL,
    "memo" varchar NOT NULL,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY ("id")
) COMMENT '学生表';

-- 将该删表 SQL 语句，添加到 yudao-module-infra-biz 模块的 test/resources/sql/clean.sql 文件里
DELETE FROM "infra_student";